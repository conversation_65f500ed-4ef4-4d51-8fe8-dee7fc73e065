#!/usr/bin/env python3
"""
YouTube Music Downloader with Apple Music Metadata Integration
Downloads tracks from YouTube Music using Apple Music metadata
"""

import os
import sys
import json
import re
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import quote
import time

# Import the Apple Music fetcher
from script import AppleMusicFetcher

class YouTubeMusicDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # Check if required tools are available
        self.check_dependencies()

    def check_dependencies(self):
        """Check if required tools are installed"""
        required_tools = ['yt-dlp', 'ffmpeg']
        missing_tools = []

        for tool in required_tools:
            try:
                subprocess.run([tool, '--version'], capture_output=True, check=True)
                print(f"✅ {tool} is available")
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing_tools.append(tool)
                print(f"❌ {tool} is not available")

        if missing_tools:
            print(f"\n🚨 Missing required tools: {', '.join(missing_tools)}")
            print("Please install them:")
            print("- yt-dlp: pip install yt-dlp")
            print("- ffmpeg: Download from https://ffmpeg.org/download.html")
            sys.exit(1)

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        # Remove multiple spaces and trim
        filename = re.sub(r'\s+', ' ', filename).strip()

        # Limit length
        if len(filename) > 200:
            filename = filename[:200]

        return filename

    def search_youtube_music(self, query: str) -> Optional[str]:
        """Search for a track on YouTube Music and return the best match URL"""
        try:
            # Use yt-dlp to search YouTube Music
            search_query = f"ytsearch1:{query} site:music.youtube.com"

            cmd = [
                'yt-dlp',
                '--quiet',
                '--no-warnings',
                '--print', 'webpage_url',
                search_query
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0 and result.stdout.strip():
                url = result.stdout.strip()
                print(f"  🔍 Found: {url}")
                return url
            else:
                print(f"  ❌ No results found for: {query}")
                return None

        except subprocess.TimeoutExpired:
            print(f"  ⏰ Search timeout for: {query}")
            return None
        except Exception as e:
            print(f"  ❌ Search error for {query}: {e}")
            return None

    def download_cover_art(self, url: str, filepath: str) -> bool:
        """Download cover art from URL"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                f.write(response.content)

            print(f"  📸 Downloaded cover art: {filepath}")
            return True
        except Exception as e:
            print(f"  ❌ Failed to download cover art: {e}")
            return False

    def download_track(self, youtube_url: str, output_path: Path, metadata: Dict) -> Optional[str]:
        """Download a track from YouTube Music and return the downloaded filename"""
        try:
            # Create a safe filename for the track
            safe_title = self.sanitize_filename(f"{metadata.get('artist', 'Unknown')} - {metadata.get('title', 'Unknown')}")
            output_file = output_path / f"{safe_title}.%(ext)s"

            cmd = [
                'yt-dlp',
                '--extract-audio',
                '--audio-format', 'mp3',
                '--audio-quality', '320K',
                '--output', str(output_file),
                '--no-playlist',
                '--quiet',
                '--no-warnings',
                '--no-check-certificate',
                youtube_url
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # Find the actual downloaded file
                expected_file = output_path / f"{safe_title}.mp3"
                if expected_file.exists():
                    print(f"  ✅ Downloaded: {expected_file.name}")
                    return str(expected_file)
                else:
                    # Look for any newly created mp3 files
                    mp3_files = list(output_path.glob("*.mp3"))
                    if mp3_files:
                        latest_file = max(mp3_files, key=os.path.getctime)
                        # Rename to expected filename
                        latest_file.rename(expected_file)
                        print(f"  ✅ Downloaded and renamed: {expected_file.name}")
                        return str(expected_file)

                print(f"  ❌ Download completed but file not found")
                return None
            else:
                print(f"  ❌ Download failed: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            print(f"  ⏰ Download timeout")
            return None
        except Exception as e:
            print(f"  ❌ Download error: {e}")
            return None

    def embed_metadata(self, audio_file: str, metadata: Dict, cover_art_file: str = None) -> bool:
        """Embed metadata into audio file using ffmpeg"""
        try:
            # Prepare metadata arguments
            meta_args = []

            if metadata.get('title'):
                meta_args.extend(['-metadata', f"title={metadata['title']}"])
            if metadata.get('artist'):
                meta_args.extend(['-metadata', f"artist={metadata['artist']}"])
            if metadata.get('album'):
                meta_args.extend(['-metadata', f"album={metadata['album']}"])
            if metadata.get('track_number'):
                meta_args.extend(['-metadata', f"track={metadata['track_number']}"])
            if metadata.get('genre'):
                meta_args.extend(['-metadata', f"genre={metadata['genre']}"])
            if metadata.get('release_date'):
                # Extract year from release date
                year = metadata['release_date'][:4] if len(metadata['release_date']) >= 4 else ''
                if year:
                    meta_args.extend(['-metadata', f"date={year}"])

            # Prepare output file
            temp_file = audio_file + '.temp.mp3'

            cmd = ['ffmpeg', '-i', audio_file, '-y']

            # Add cover art if available
            if cover_art_file and os.path.exists(cover_art_file):
                cmd.extend(['-i', cover_art_file, '-map', '0:a', '-map', '1:v'])
                cmd.extend(['-c:a', 'copy', '-c:v', 'mjpeg'])
                cmd.extend(['-disposition:v', 'attached_pic'])
            else:
                cmd.extend(['-c:a', 'copy'])

            # Add metadata
            cmd.extend(meta_args)
            cmd.append(temp_file)

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # Replace original file with metadata-embedded version
                os.replace(temp_file, audio_file)
                print(f"  🏷️ Metadata embedded successfully")
                return True
            else:
                print(f"  ❌ Metadata embedding failed: {result.stderr}")
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                return False

        except Exception as e:
            print(f"  ❌ Metadata embedding error: {e}")
            return False

    def create_m3u8_playlist(self, playlist_dir: Path, tracks: List[Dict], playlist_name: str):
        """Create M3U8 playlist file"""
        try:
            m3u8_file = playlist_dir / f"{self.sanitize_filename(playlist_name)}.m3u8"

            with open(m3u8_file, 'w', encoding='utf-8') as f:
                f.write("#EXTM3U\n")
                f.write(f"#PLAYLIST:{playlist_name}\n\n")

                for track in tracks:
                    if track.get('downloaded_file'):
                        duration = track.get('duration_ms', 0) // 1000 if track.get('duration_ms') else 0
                        artist = track.get('artist', 'Unknown Artist')
                        title = track.get('title', 'Unknown Title')

                        f.write(f"#EXTINF:{duration},{artist} - {title}\n")
                        f.write(f"{os.path.basename(track['downloaded_file'])}\n\n")

            print(f"📋 Created playlist file: {m3u8_file}")

        except Exception as e:
            print(f"❌ Failed to create M3U8 playlist: {e}")

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python downloader.py <apple_music_playlist_url>")
        print("Example: python downloader.py 'https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12'")
        sys.exit(1)

    apple_music_url = sys.argv[1]

    print("🎵 Apple Music to YouTube Music Downloader")
    print("=" * 50)

    # Initialize components
    fetcher = AppleMusicFetcher()
    downloader = YouTubeMusicDownloader()

    # Fetch Apple Music metadata
    print("📋 Fetching Apple Music metadata...")
    metadata = fetcher.fetch_metadata(apple_music_url, fetch_track_details=True)

    if 'error' in metadata:
        print(f"❌ Error fetching metadata: {metadata['error']}")
        sys.exit(1)

    playlist_name = metadata['title']
    tracks = metadata['tracks']

    print(f"📁 Playlist: {playlist_name}")
    print(f"🎵 Tracks: {len(tracks)}")

    # Create playlist directory
    safe_playlist_name = downloader.sanitize_filename(playlist_name)
    playlist_dir = Path(safe_playlist_name)
    playlist_dir.mkdir(exist_ok=True)

    print(f"📂 Created directory: {playlist_dir}")

    # Download playlist cover art
    if metadata.get('cover_art'):
        cover_art_file = playlist_dir / "cover.jpg"
        print(f"📸 Downloading playlist cover art...")
        downloader.download_cover_art(metadata['cover_art'], str(cover_art_file))

    # Process each track
    successful_downloads = []

    for i, track in enumerate(tracks, 1):
        print(f"\n🎵 Processing track {i}/{len(tracks)}: {track['title']}")

        # Create search query
        search_query = f"{track['artist']} {track['title']}"
        print(f"  🔍 Searching: {search_query}")

        # Search on YouTube Music
        youtube_url = downloader.search_youtube_music(search_query)

        if not youtube_url:
            print(f"  ⏭️ Skipping track (not found)")
            continue

        # Download track cover art
        track_cover_file = None
        if track.get('cover_art'):
            track_cover_file = playlist_dir / f"cover_{i}.jpg"
            downloader.download_cover_art(track['cover_art'], str(track_cover_file))

        # Download audio
        print(f"  ⬇️ Downloading audio...")
        downloaded_file = downloader.download_track(youtube_url, playlist_dir, track)

        if downloaded_file:
            # Embed metadata
            print(f"  🏷️ Embedding metadata...")
            downloader.embed_metadata(downloaded_file, track, str(track_cover_file) if track_cover_file else None)

            # Clean up individual cover art file (it's now embedded)
            if track_cover_file and track_cover_file.exists():
                track_cover_file.unlink()

            track['downloaded_file'] = downloaded_file
            successful_downloads.append(track)
        else:
            print(f"  ⏭️ Skipping track (download failed)")

        # Small delay to be respectful
        time.sleep(1)

    # Create M3U8 playlist
    if successful_downloads:
        print(f"\n📋 Creating M3U8 playlist...")
        downloader.create_m3u8_playlist(playlist_dir, successful_downloads, playlist_name)

    print(f"\n✅ Download complete!")
    print(f"📁 Location: {playlist_dir}")
    print(f"🎵 Successfully downloaded: {len(successful_downloads)}/{len(tracks)} tracks")

if __name__ == "__main__":
    main()
