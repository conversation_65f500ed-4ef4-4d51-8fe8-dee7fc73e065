#!/usr/bin/env python3
"""
YouTube Music Downloader with Apple Music Metadata Integration
Downloads tracks from YouTube Music using Apple Music metadata
"""

import os
import sys
import re
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import time
from tqdm import tqdm

# Import the Apple Music fetcher
from script import AppleMusicFetcher

class YouTubeMusicDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # Check if required tools are available
        self.check_dependencies()

        # Set up base download directory
        self.base_download_dir = self.get_base_download_directory()

    def get_base_download_directory(self) -> Path:
        """Get the base download directory in user's Downloads folder"""
        # Get user's Downloads folder
        downloads_dir = Path.home() / "Downloads"

        # Create sthashApplemusic directory
        base_dir = downloads_dir / "sthashApplemusic"
        base_dir.mkdir(exist_ok=True)

        return base_dir

    def check_dependencies(self):
        """Check if required tools are installed"""
        required_tools = ['yt-dlp', 'ffmpeg']
        missing_tools = []

        for tool in required_tools:
            try:
                # For ffmpeg, just check if it runs (it may return non-zero for --version)
                result = subprocess.run([tool, '--version'], capture_output=True, timeout=10)
                if result.stdout or result.stderr:
                    print(f"✅ {tool} is available")
                else:
                    missing_tools.append(tool)
                    print(f"❌ {tool} is not available")
            except subprocess.TimeoutExpired:
                print(f"⏰ {tool} check timed out, but likely available")
            except FileNotFoundError:
                missing_tools.append(tool)
                print(f"❌ {tool} is not available")

        if missing_tools:
            print(f"\n🚨 Missing required tools: {', '.join(missing_tools)}")
            print("Please install them:")
            print("- yt-dlp: pip install yt-dlp")
            print("- ffmpeg: Download from https://ffmpeg.org/download.html")
            sys.exit(1)

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        # Remove multiple spaces and trim
        filename = re.sub(r'\s+', ' ', filename).strip()

        # Limit length
        if len(filename) > 200:
            filename = filename[:200]

        return filename

    def create_download_directory(self, metadata: Dict) -> Path:
        """Create appropriate directory structure based on content type"""
        content_type = metadata.get('type', 'unknown')

        if content_type == 'track':
            # Single track: artist/album/
            artist = self.sanitize_filename(metadata.get('artist', 'Unknown Artist'))
            album = self.sanitize_filename(metadata.get('album', 'Unknown Album'))
            download_dir = self.base_download_dir / artist / album

        elif content_type == 'album':
            # Album: artist/album/
            artist = self.sanitize_filename(metadata.get('artist', 'Unknown Artist'))
            album = self.sanitize_filename(metadata.get('title', 'Unknown Album'))
            download_dir = self.base_download_dir / artist / album

        elif content_type == 'playlist':
            # Playlist: playlist_name/
            playlist_name = self.sanitize_filename(metadata.get('title', 'Unknown Playlist'))
            download_dir = self.base_download_dir / playlist_name

        else:
            # Fallback
            title = self.sanitize_filename(metadata.get('title', 'Unknown'))
            download_dir = self.base_download_dir / title

        # Create the directory
        download_dir.mkdir(parents=True, exist_ok=True)
        return download_dir

    def search_youtube_music(self, query: str) -> Optional[str]:
        """Search for a track on YouTube Music with multiple strategies"""
        # Try multiple search strategies
        search_strategies = [
            f"{query} audio",  # Original query + audio
            f"{query.replace(' & ', ' ')} audio",  # Remove & symbols + audio
            f"{query.replace(' feat. ', ' ')} audio",  # Remove feat. + audio
            f"{query.replace(' ft. ', ' ')} audio",  # Remove ft. + audio
            f"{query.replace(' featuring ', ' ')} audio",  # Remove featuring + audio
        ]

        # If query has multiple artists, try with just the first artist
        if " & " in query or " feat" in query.lower() or " ft" in query.lower():
            parts = query.split()
            if len(parts) >= 2:
                # Try with just the first word (likely main artist) + song title + audio
                first_artist = parts[0]
                song_title = " ".join(parts[1:])
                search_strategies.append(f"{first_artist} {song_title} audio")

                # Try with just the song title + audio
                search_strategies.append(f"{song_title} audio")

        # Remove duplicates while preserving order
        seen = set()
        search_strategies = [x for x in search_strategies if not (x in seen or seen.add(x))]

        for i, search_query in enumerate(search_strategies):
            if i > 0:
                print(f"  🔄 Trying alternative search: {search_query}")

            try:
                # Use yt-dlp to search YouTube Music
                yt_search_query = f"ytsearch3:{search_query}"  # Get top 3 results

                cmd = [
                    'yt-dlp',
                    '--quiet',
                    '--no-warnings',
                    '--print', 'webpage_url',
                    '--print', 'title',
                    yt_search_query
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                if result.returncode == 0 and result.stdout.strip():
                    lines = result.stdout.strip().split('\n')

                    # Parse results (alternating URL and title)
                    for j in range(0, len(lines), 2):
                        if j + 1 < len(lines):
                            url = lines[j]
                            title = lines[j + 1]

                            # Simple relevance check
                            if self._is_relevant_result(search_query, title):
                                print(f"  🔍 Found: {url}")
                                print(f"  📝 Title: {title}")
                                return url

                    # If no relevant result found, return the first one
                    if lines:
                        url = lines[0]
                        title = lines[1] if len(lines) > 1 else "Unknown"
                        print(f"  🔍 Found (best guess): {url}")
                        print(f"  📝 Title: {title}")
                        return url

            except subprocess.TimeoutExpired:
                print(f"  ⏰ Search timeout for: {search_query}")
                continue
            except Exception as e:
                print(f"  ❌ Search error for {search_query}: {e}")
                continue

        print(f"  ❌ No results found after trying all search strategies")
        return None

    def _is_relevant_result(self, search_query: str, result_title: str) -> bool:
        """Check if a search result is relevant to the query"""
        search_lower = search_query.lower()
        title_lower = result_title.lower()

        # Extract words from search query (remove common words)
        search_words = [w for w in search_lower.split() if w not in ['the', 'a', 'an', 'and', 'or', 'but', 'feat', 'ft', 'featuring']]

        # Check if at least 50% of search words are in the title
        matches = sum(1 for word in search_words if word in title_lower)
        relevance_score = matches / len(search_words) if search_words else 0

        return relevance_score >= 0.5

    def download_cover_art(self, url: str, filepath: str, show_progress: bool = False) -> bool:
        """Download cover art from URL with optional progress bar"""
        try:
            response = self.session.get(url, stream=True, timeout=10)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))

            if show_progress and total_size > 0:
                with open(filepath, 'wb') as f:
                    with tqdm(
                        total=total_size,
                        unit='B',
                        unit_scale=True,
                        desc="  📸 Cover Art",
                        leave=False,
                        bar_format='{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]'
                    ) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
            else:
                with open(filepath, 'wb') as f:
                    f.write(response.content)

            if not show_progress:
                print(f"  📸 Downloaded cover art: {os.path.basename(filepath)}")
            return True
        except Exception as e:
            print(f"  ❌ Failed to download cover art: {e}")
            return False

    def download_track(self, youtube_url: str, output_path: Path, metadata: Dict, track_number: int = None, content_type: str = 'track') -> Optional[str]:
        """Download a track from YouTube Music and return the downloaded filename"""
        try:
            # Create a safe filename for the track
            artist = metadata.get('artist', 'Unknown')
            title = metadata.get('title', 'Unknown')

            # Create filename based on content type
            if content_type in ['album', 'playlist'] and track_number is not None:
                # For albums and playlists, include track number
                safe_title = self.sanitize_filename(f"{track_number:02d}. {artist} - {title}")
            else:
                # For single tracks, no number
                safe_title = self.sanitize_filename(f"{artist} - {title}")

            output_file = output_path / f"{safe_title}.%(ext)s"

            cmd = [
                'yt-dlp',
                '--extract-audio',
                '--audio-format', 'mp3',
                '--audio-quality', '320K',
                '--output', str(output_file),
                '--no-playlist',
                '--quiet',
                '--no-warnings',
                '--no-check-certificate',
                youtube_url
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # Find the actual downloaded file
                expected_file = output_path / f"{safe_title}.mp3"
                if expected_file.exists():
                    print(f"  ✅ Downloaded: {expected_file.name}")
                    return str(expected_file)
                else:
                    # Look for any newly created mp3 files
                    mp3_files = list(output_path.glob("*.mp3"))
                    if mp3_files:
                        latest_file = max(mp3_files, key=os.path.getctime)
                        # Rename to expected filename
                        latest_file.rename(expected_file)
                        print(f"  ✅ Downloaded and renamed: {expected_file.name}")
                        return str(expected_file)

                print(f"  ❌ Download completed but file not found")
                return None
            else:
                print(f"  ❌ Download failed: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            print(f"  ⏰ Download timeout")
            return None
        except Exception as e:
            print(f"  ❌ Download error: {e}")
            return None

    def embed_metadata(self, audio_file: str, metadata: Dict, cover_art_file: str = None) -> bool:
        """Embed metadata into audio file using ffmpeg"""
        try:
            # Prepare metadata arguments
            meta_args = []

            if metadata.get('title'):
                meta_args.extend(['-metadata', f"title={metadata['title']}"])
            if metadata.get('artist'):
                meta_args.extend(['-metadata', f"artist={metadata['artist']}"])
            if metadata.get('album'):
                meta_args.extend(['-metadata', f"album={metadata['album']}"])
            if metadata.get('track_number'):
                meta_args.extend(['-metadata', f"track={metadata['track_number']}"])
            if metadata.get('genre'):
                meta_args.extend(['-metadata', f"genre={metadata['genre']}"])
            if metadata.get('release_date'):
                # Extract year from release date
                year = metadata['release_date'][:4] if len(metadata['release_date']) >= 4 else ''
                if year:
                    meta_args.extend(['-metadata', f"date={year}"])

            # Prepare output file
            temp_file = audio_file + '.temp.mp3'

            cmd = ['ffmpeg', '-i', audio_file, '-y']

            # Add cover art if available
            if cover_art_file and os.path.exists(cover_art_file):
                cmd.extend(['-i', cover_art_file, '-map', '0:a', '-map', '1:v'])
                cmd.extend(['-c:a', 'copy', '-c:v', 'mjpeg'])
                cmd.extend(['-disposition:v', 'attached_pic'])
            else:
                cmd.extend(['-c:a', 'copy'])

            # Add metadata
            cmd.extend(meta_args)
            cmd.append(temp_file)

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # Replace original file with metadata-embedded version
                os.replace(temp_file, audio_file)
                print(f"  🏷️ Metadata embedded successfully")
                return True
            else:
                print(f"  ❌ Metadata embedding failed: {result.stderr}")
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                return False

        except Exception as e:
            print(f"  ❌ Metadata embedding error: {e}")
            return False

    def create_m3u8_playlist(self, playlist_dir: Path, tracks: List[Dict], playlist_name: str):
        """Create M3U8 playlist file"""
        try:
            m3u8_file = playlist_dir / f"{self.sanitize_filename(playlist_name)}.m3u8"

            with open(m3u8_file, 'w', encoding='utf-8') as f:
                f.write("#EXTM3U\n")
                f.write(f"#PLAYLIST:{playlist_name}\n\n")

                for track in tracks:
                    if track.get('downloaded_file'):
                        duration = track.get('duration_ms', 0) // 1000 if track.get('duration_ms') else 0
                        artist = track.get('artist', 'Unknown Artist')
                        title = track.get('title', 'Unknown Title')

                        f.write(f"#EXTINF:{duration},{artist} - {title}\n")
                        f.write(f"{os.path.basename(track['downloaded_file'])}\n\n")

            print(f"📋 Created playlist file: {m3u8_file}")

        except Exception as e:
            print(f"❌ Failed to create M3U8 playlist: {e}")

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python downloader.py <apple_music_playlist_url>")
        print("Example: python downloader.py 'https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12'")
        sys.exit(1)

    apple_music_url = sys.argv[1]

    print("🎵 Apple Music to YouTube Music Downloader")
    print("=" * 50)

    # Initialize components
    fetcher = AppleMusicFetcher()
    downloader = YouTubeMusicDownloader()

    # Fetch Apple Music metadata
    print("📋 Fetching Apple Music metadata...")
    metadata = fetcher.fetch_metadata(apple_music_url, fetch_track_details=True)

    if 'error' in metadata:
        print(f"❌ Error fetching metadata: {metadata['error']}")
        sys.exit(1)

    # Handle different content types
    content_type = metadata['type']

    if content_type == 'track':
        # Single track
        tracks = [metadata]  # Convert single track to list
        print(f"🎵 Single Track: {metadata['title']} by {metadata['artist']}")
        print(f"📁 Album: {metadata.get('album', 'Unknown Album')}")

    elif content_type == 'album':
        # Album
        tracks = metadata.get('tracks', [])
        print(f"💿 Album: {metadata['title']} by {metadata['artist']}")
        print(f"🎵 Tracks: {len(tracks)}")

    elif content_type == 'playlist':
        # Playlist
        tracks = metadata.get('tracks', [])
        print(f"📋 Playlist: {metadata['title']}")
        print(f"🎵 Tracks: {len(tracks)}")

    else:
        print(f"❌ Unsupported content type: {content_type}")
        sys.exit(1)

    if not tracks:
        print("❌ No tracks found to download")
        sys.exit(1)

    # Create appropriate directory structure
    download_dir = downloader.create_download_directory(metadata)
    print(f"📂 Download directory: {download_dir}")

    # Download main cover art
    main_cover_art_url = None
    if content_type == 'track':
        main_cover_art_url = metadata.get('cover_art')
    elif content_type == 'album':
        main_cover_art_url = metadata.get('cover_art')
    elif content_type == 'playlist':
        main_cover_art_url = metadata.get('cover_art')

    main_cover_art_file = None
    if main_cover_art_url:
        main_cover_art_file = download_dir / "cover.jpg"
        print(f"📸 Downloading {content_type} cover art...")
        downloader.download_cover_art(main_cover_art_url, str(main_cover_art_file))

    # Process each track with progress bar
    successful_downloads = []

    print(f"\n🎵 Starting download of {len(tracks)} track{'s' if len(tracks) != 1 else ''}...")

    # Create main progress bar
    with tqdm(
        total=len(tracks),
        desc="🎵 Overall Progress",
        unit="track",
        bar_format='{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} tracks [{elapsed}<{remaining}]',
        position=0,
        leave=True
    ) as main_pbar:

            for i, track in enumerate(tracks, 1):
                # Update progress bar description with current track
                track_title = track.get('title', 'Unknown Title')
                track_artist = track.get('artist', '')

                # For albums, if track doesn't have artist, use album artist
                if not track_artist and content_type == 'album':
                    track_artist = metadata.get('artist', 'Unknown Artist')
                    track['artist'] = track_artist  # Update the track data

                # Ensure we have an artist
                if not track_artist:
                    track_artist = 'Unknown Artist'
                    track['artist'] = track_artist

                # Update progress bar with current track info
                main_pbar.set_description(f"🎵 {i}/{len(tracks)}: {track_title[:30]}{'...' if len(track_title) > 30 else ''}")

                # Create search query
                search_query = f"{track_artist} {track_title}"

                # Search on YouTube Music
                youtube_url = downloader.search_youtube_music(search_query)

                if not youtube_url:
                    tqdm.write(f"  ⏭️ Skipping track {i}: {track_title} (not found)")
                    main_pbar.update(1)
                    continue

                # Download individual track cover art for all content types
                track_cover_file = None
                if track.get('cover_art'):
                    track_cover_file = download_dir / f"cover_{i}.jpg"
                    tqdm.write(f"  📸 Downloading track cover art...")

                    # Extract the highest resolution URL from cover art data
                    cover_art_data = track['cover_art']
                    if isinstance(cover_art_data, dict):
                        # Get the highest resolution available
                        cover_art_url = (cover_art_data.get('original') or
                                       cover_art_data.get('ultra_high') or
                                       cover_art_data.get('extra_large') or
                                       cover_art_data.get('large') or
                                       cover_art_data.get('medium') or
                                       cover_art_data.get('small') or
                                       cover_art_data.get('base'))
                    else:
                        # If it's already a string URL, use it directly
                        cover_art_url = cover_art_data

                    if cover_art_url:
                        downloader.download_cover_art(cover_art_url, str(track_cover_file))
                    else:
                        tqdm.write(f"  ⚠️ No valid cover art URL found")
                        track_cover_file = None

                # Download audio
                tqdm.write(f"  ⬇️ Downloading audio for: {track_title}")
                downloaded_file = downloader.download_track(youtube_url, download_dir, track, i, content_type)

                if downloaded_file:
                    # Embed metadata
                    tqdm.write(f"  🏷️ Embedding metadata...")

                    # For playlists: Always use individual track cover art if available
                    # For albums/singles: Use track cover art if available, otherwise use main cover art
                    if content_type == 'playlist':
                        # For playlists, prioritize individual track cover art
                        cover_for_embedding = str(track_cover_file) if track_cover_file and track_cover_file.exists() else None
                    else:
                        # For albums/singles, use track cover art if available, otherwise use main cover art
                        cover_for_embedding = str(track_cover_file) if track_cover_file and track_cover_file.exists() else str(main_cover_art_file) if main_cover_art_file else None

                    downloader.embed_metadata(downloaded_file, track, cover_for_embedding)

                    # Clean up individual track cover art file (it's now embedded)
                    if track_cover_file and track_cover_file.exists():
                        track_cover_file.unlink()

                    track['downloaded_file'] = downloaded_file
                    successful_downloads.append(track)
                    tqdm.write(f"  ✅ Completed: {track_title}")
                else:
                    tqdm.write(f"  ⏭️ Skipping track {i}: {track_title} (download failed)")

                # Update progress bar
                main_pbar.update(1)

                # Small delay to be respectful
                time.sleep(0.5)

    # Create M3U8 playlist (only for multiple tracks)
    if successful_downloads and len(successful_downloads) > 1:
        print(f"\n📋 Creating M3U8 playlist...")
        playlist_title = metadata['title']
        downloader.create_m3u8_playlist(download_dir, successful_downloads, playlist_title)
    elif successful_downloads and len(successful_downloads) == 1:
        print(f"\n📋 Single track downloaded - no playlist file needed")

    print(f"\n✅ Download complete!")
    print(f"📁 Location: {download_dir}")
    print(f"🎵 Successfully downloaded: {len(successful_downloads)}/{len(tracks)} tracks")

if __name__ == "__main__":
    main()
