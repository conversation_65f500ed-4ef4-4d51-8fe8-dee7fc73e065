#!/usr/bin/env python3
"""
Test album download functionality
"""

from script import AppleMusicFetcher

def test_album_metadata():
    """Test album metadata fetching"""
    # Test with a small album
    album_url = "https://music.apple.com/us/album/let-me-fly/1802103974"
    
    print("🧪 Testing album metadata...")
    print(f"URL: {album_url}")
    
    fetcher = AppleMusicFetcher()
    
    # Get album metadata
    print("\n📋 Fetching album metadata...")
    metadata = fetcher.fetch_metadata(album_url, fetch_track_details=True)
    
    if 'error' in metadata:
        print(f"❌ Error: {metadata['error']}")
        return
    
    print(f"✅ Found album: {metadata['title']}")
    print(f"🎤 Artist: {metadata.get('artist', 'Unknown')}")
    print(f"📊 Tracks: {len(metadata.get('tracks', []))}")
    print(f"🎵 Type: {metadata['type']}")
    
    # Show tracks
    tracks = metadata.get('tracks', [])
    if tracks:
        print(f"\n🎵 Album tracks:")
        for i, track in enumerate(tracks[:5], 1):  # Show first 5 tracks
            print(f"  {i:02d}. {track['title']} - {track['artist']}")
            print(f"      Album: {track.get('album', 'N/A')}")
            print(f"      Duration: {track.get('duration_ms', 0) // 1000}s")
    
    print(f"\n📋 Directory structure would be:")
    print(f"Downloads/sthashApplemusic/{metadata.get('artist', 'Unknown')}/{metadata['title']}/")
    print(f"├── cover.jpg")
    for i, track in enumerate(tracks[:3], 1):
        print(f"├── {i:02d}. {track['artist']} - {track['title']}.mp3")
    if len(tracks) > 3:
        print(f"├── ... ({len(tracks) - 3} more tracks)")
    print(f"└── {metadata['title']}.m3u8")
    
    print("\n✅ Album metadata test complete!")

if __name__ == "__main__":
    test_album_metadata()
