#!/usr/bin/env python3
"""
Test playlist download structure (metadata only)
"""

from script import AppleMusicFetcher

def test_playlist_structure():
    """Test playlist metadata and show expected structure"""
    # Test with a small playlist
    playlist_url = "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
    
    print("🧪 Testing playlist structure...")
    print(f"URL: {playlist_url}")
    
    fetcher = AppleMusicFetcher()
    
    # Get playlist metadata (just first few tracks)
    print("\n📋 Fetching playlist metadata...")
    metadata = fetcher.fetch_metadata(playlist_url, fetch_track_details=False)
    
    if 'error' in metadata:
        print(f"❌ Error: {metadata['error']}")
        return
    
    print(f"✅ Found playlist: {metadata['title']}")
    print(f"📊 Total tracks: {len(metadata.get('tracks', []))}")
    print(f"🎵 Type: {metadata['type']}")
    
    # Show first 5 tracks
    tracks = metadata.get('tracks', [])
    if tracks:
        print(f"\n🎵 First 5 tracks:")
        for i, track in enumerate(tracks[:5], 1):
            artist = track.get('artist', 'Unknown Artist')
            title = track.get('title', 'Unknown Title')
            print(f"  {i:02d}. {title} - {artist}")
    
    print(f"\n📋 Directory structure would be:")
    print(f"Downloads/sthashApplemusic/{metadata['title']}/")
    print(f"├── cover.jpg")
    for i, track in enumerate(tracks[:5], 1):
        artist = track.get('artist', 'Unknown Artist')
        title = track.get('title', 'Unknown Title')
        print(f"├── {i:02d}. {artist} - {title}.mp3")
    if len(tracks) > 5:
        print(f"├── ... ({len(tracks) - 5} more tracks)")
    print(f"└── {metadata['title']}.m3u8")
    
    print("\n✅ Playlist structure test complete!")

if __name__ == "__main__":
    test_playlist_structure()
