#!/usr/bin/env python3
"""
Quick test of the downloader with a smaller playlist
"""

import sys
import json
from script import AppleMusicFetcher

def quick_test():
    """Quick test with just metadata fetching"""
    # Test with a smaller playlist or single track
    test_urls = [
        "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
    ]
    
    fetcher = AppleMusicFetcher()
    
    for url in test_urls:
        print(f"🧪 Testing: {url}")
        
        # Get basic metadata
        metadata = fetcher.fetch_metadata(url, fetch_track_details=False)
        
        if 'error' in metadata:
            print(f"❌ Error: {metadata['error']}")
            continue
        
        print(f"✅ Found playlist: {metadata['title']}")
        print(f"📊 Tracks: {len(metadata['tracks'])}")
        
        # Show first 3 tracks
        for i, track in enumerate(metadata['tracks'][:3], 1):
            print(f"  {i}. {track['title']} - {track['artist']} ({track['album']})")
            print(f"     Cover: {track['cover_art'][:50]}...")
        
        print(f"\n📋 Sample JSON structure:")
        sample = {
            'playlist_title': metadata['title'],
            'total_tracks': len(metadata['tracks']),
            'sample_track': metadata['tracks'][0] if metadata['tracks'] else {}
        }
        
        print(json.dumps(sample, indent=2)[:500] + "...")
        break

if __name__ == "__main__":
    quick_test()
