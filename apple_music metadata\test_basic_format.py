#!/usr/bin/env python3
"""
Test the basic format with artist, album, and cover art
"""

import sys
import json
from script import AppleMusicFetcher

def test_basic_format():
    """Test the basic format with artist, album, and cover art"""
    url = "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
    
    fetcher = AppleMusicFetcher()
    
    print("Fetching basic playlist metadata...", file=sys.stderr)
    metadata = fetcher.fetch_metadata(url, fetch_track_details=False)
    
    if 'error' in metadata:
        print(f"Error: {metadata['error']}", file=sys.stderr)
        return
    
    # Show just the first 3 tracks to demonstrate the format
    sample_tracks = metadata['tracks'][:3]
    
    # Create the result with just the tracks to show the format
    result = {
        'playlist_title': metadata['title'],
        'total_tracks': len(metadata['tracks']),
        'sample_tracks': sample_tracks
    }
    
    # Output the JSON
    try:
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except UnicodeEncodeError:
        print(json.dumps(result, indent=2, ensure_ascii=True))

if __name__ == "__main__":
    test_basic_format()
