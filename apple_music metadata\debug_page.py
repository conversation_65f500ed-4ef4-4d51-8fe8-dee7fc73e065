#!/usr/bin/env python3
"""
Debug script to examine Apple Music page structure
"""

import requests
import re
import json
import sys

def debug_apple_music_page(url):
    """Debug the Apple Music page structure"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })

    print(f"Fetching: {url}")
    response = session.get(url, timeout=15)
    response.raise_for_status()

    html = response.text
    print(f"Page size: {len(html)} characters")

    # Check for script tags with JSON data
    print("\n=== CHECKING FOR SCRIPT TAGS WITH JSON DATA ===")
    script_patterns = [
        r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
        r'window\.__NUXT__\s*=\s*({.*?});',
        r'__APOLLO_STATE__\s*=\s*({.*?});',
        r'window\.MusicKit\s*=\s*({.*?});',
        r'window\.__NEXT_DATA__\s*=\s*({.*?});'
    ]

    for i, pattern in enumerate(script_patterns):
        print(f"\nPattern {i+1}: {pattern[:50]}...")
        matches = re.findall(pattern, html, re.DOTALL)
        if matches:
            print(f"  Found {len(matches)} matches")
            for j, match in enumerate(matches[:2]):  # Only show first 2 matches
                print(f"  Match {j+1} length: {len(match)} chars")
                try:
                    data = json.loads(match)
                    print(f"  Match {j+1} parsed successfully as JSON")
                    # Look for track-like data
                    track_count = count_potential_tracks(data)
                    print(f"  Potential tracks found: {track_count}")
                except json.JSONDecodeError as e:
                    print(f"  Match {j+1} JSON parse error: {e}")
        else:
            print("  No matches found")

    # Check for JSON-LD structured data
    print("\n=== CHECKING FOR JSON-LD STRUCTURED DATA ===")
    json_ld_pattern = r'<script type="application/ld\+json"[^>]*>(.*?)</script>'
    json_matches = re.findall(json_ld_pattern, html, re.DOTALL)

    if json_matches:
        print(f"Found {len(json_matches)} JSON-LD script tags")
        for i, json_str in enumerate(json_matches):
            print(f"\nJSON-LD {i+1} length: {len(json_str)} chars")
            try:
                data = json.loads(json_str.strip())
                print(f"JSON-LD {i+1} parsed successfully")
                print(f"Type: {data.get('@type') if isinstance(data, dict) else 'list'}")
                if isinstance(data, dict) and data.get('@type') == 'MusicPlaylist':
                    tracks = data.get('track', [])
                    print(f"MusicPlaylist has {len(tracks)} tracks")
                elif isinstance(data, list):
                    for j, item in enumerate(data):
                        if isinstance(item, dict) and item.get('@type') == 'MusicPlaylist':
                            tracks = item.get('track', [])
                            print(f"MusicPlaylist in list item {j} has {len(tracks)} tracks")
            except json.JSONDecodeError as e:
                print(f"JSON-LD {i+1} parse error: {e}")
    else:
        print("No JSON-LD script tags found")

    # Check for common DOM patterns
    print("\n=== CHECKING FOR DOM PATTERNS ===")
    dom_patterns = [
        r'<div[^>]*data-testid="track-list-item"[^>]*>',
        r'<tr[^>]*class="[^"]*track[^"]*"[^>]*>',
        r'<li[^>]*class="[^"]*song[^"]*"[^>]*>',
        r'<div[^>]*class="[^"]*track[^"]*"[^>]*>',
        r'data-testid="[^"]*track[^"]*"',
        r'class="[^"]*song-list[^"]*"',
        r'class="[^"]*tracklist[^"]*"'
    ]

    for i, pattern in enumerate(dom_patterns):
        matches = re.findall(pattern, html, re.IGNORECASE)
        print(f"Pattern {i+1} ({pattern[:30]}...): {len(matches)} matches")

    # Look for any script tags containing "track" or "song"
    print("\n=== CHECKING FOR SCRIPTS WITH TRACK/SONG KEYWORDS ===")
    script_tag_pattern = r'<script[^>]*>(.*?)</script>'
    script_matches = re.findall(script_tag_pattern, html, re.DOTALL)

    track_scripts = 0
    for script in script_matches:
        if 'track' in script.lower() or 'song' in script.lower():
            track_scripts += 1

    print(f"Found {len(script_matches)} total script tags")
    print(f"Found {track_scripts} script tags containing 'track' or 'song'")

    # Save a sample of the HTML for manual inspection
    sample_size = min(50000, len(html))
    with open('page_sample.html', 'w', encoding='utf-8') as f:
        f.write(html[:sample_size])
    print(f"\nSaved first {sample_size} characters to page_sample.html")

def count_potential_tracks(obj, depth=0, max_depth=10):
    """Recursively count potential track objects"""
    if depth > max_depth:
        return 0

    count = 0

    if isinstance(obj, dict):
        # Check if this object looks like a track
        track_indicators = ['trackName', 'songName', 'title', 'name']
        artist_indicators = ['artistName', 'artist']

        has_track_name = any(key in obj for key in track_indicators)
        has_artist = any(key in obj for key in artist_indicators)

        if has_track_name and has_artist:
            count += 1

        # Recurse into nested objects
        for value in obj.values():
            if isinstance(value, (dict, list)):
                count += count_potential_tracks(value, depth + 1, max_depth)

    elif isinstance(obj, list):
        for item in obj:
            if isinstance(item, (dict, list)):
                count += count_potential_tracks(item, depth + 1, max_depth)

    return count

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python debug_page.py <apple_music_url>")
        sys.exit(1)

    url = sys.argv[1]
    debug_apple_music_page(url)
