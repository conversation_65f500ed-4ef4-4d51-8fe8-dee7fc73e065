# Apple Music to YouTube Music Downloader

A comprehensive Python toolkit for extracting Apple Music metadata and downloading corresponding tracks from YouTube Music with full metadata embedding and playlist organization.

## 🎯 Overview

This project consists of two main components:

1. **`script.py`** - Apple Music metadata fetcher
2. **`downloader.py`** - YouTube Music downloader with metadata integration

## 🚀 Quick Start

### 1. Setup
```bash
# Install dependencies
python setup.py

# Or manually install
pip install -r requirements.txt
```

### 2. Download a Playlist
```bash
python downloader.py "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
```

## 📋 Features

### 🎵 Apple Music Metadata Fetcher (`script.py`)
- **Complete Track Info**: Title, artist, album, duration, track number
- **Cover Art**: Multiple resolutions (100x100 to 1000x1000px)
- **Extended Metadata**: Genre, release date, explicit flags, preview URLs
- **Playlist Support**: Full playlist metadata with curator info
- **Rate Limited**: Respectful API usage with proper delays

### 🎧 YouTube Music Downloader (`downloader.py`)
- **Smart Search**: Finds tracks on YouTube Music using Apple Music metadata
- **High Quality**: Downloads 320kbps MP3 audio
- **Metadata Embedding**: Embeds complete metadata into audio files
- **Cover Art Integration**: Downloads and embeds album artwork
- **Playlist Organization**: Creates organized directories with M3U8 playlists
- **Sanitized Filenames**: Safe file naming for cross-platform compatibility

## 📁 Output Structure

When you download a playlist, you get:

```
Playlist Name/
├── cover.jpg                    # Playlist cover art
├── Artist - Track 1.mp3         # Audio with embedded metadata & cover
├── Artist - Track 2.mp3         # Audio with embedded metadata & cover
├── Artist - Track 3.mp3         # Audio with embedded metadata & cover
├── ...
└── Playlist Name.m3u8           # M3U8 playlist file
```

Each MP3 file includes:
- ✅ **Embedded metadata** (title, artist, album, track number, genre, year)
- ✅ **Embedded cover art** (high-resolution album artwork)
- ✅ **Proper filename** (sanitized for safe storage)

## 🛠️ Requirements

### Python Packages
- `requests` - HTTP requests
- `yt-dlp` - YouTube downloading

### External Tools
- **FFmpeg** - Audio processing and metadata embedding
- **yt-dlp** - YouTube Music downloading

## 📖 Usage Examples

### Metadata Only (Fast)
```bash
# Get basic track info with artist, album, and cover art
python script.py "https://music.apple.com/us/playlist/..."

# Get detailed metadata with extended info
python script.py "https://music.apple.com/us/playlist/..." --detailed
```

### Download Complete Playlist
```bash
# Download entire playlist with metadata and cover art
python downloader.py "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
```

### Supported URL Types
```bash
# Playlists
python downloader.py "https://music.apple.com/us/playlist/name/pl.xxxxx"

# Albums
python downloader.py "https://music.apple.com/us/album/name/xxxxx"

# Individual tracks
python downloader.py "https://music.apple.com/us/song/name/xxxxx"
```

## 🔧 Installation

### Automatic Setup
```bash
python setup.py
```

### Manual Installation

1. **Install Python packages:**
```bash
pip install requests yt-dlp
```

2. **Install FFmpeg:**

**Windows:**
```bash
# Using chocolatey
choco install ffmpeg

# Using winget
winget install FFmpeg

# Or download from: https://ffmpeg.org/download.html
```

**macOS:**
```bash
brew install ffmpeg
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg

# Fedora
sudo dnf install ffmpeg
```

## 📊 Example JSON Output

### Basic Mode
```json
{
  "title": "What I Want",
  "artist": "Morgan Wallen & Tate McRae",
  "album": "I'm The Problem",
  "duration_ms": 184517,
  "url": "https://music.apple.com/us/song/what-i-want/1802103975",
  "track_number": 1,
  "cover_art": "https://...300x300bb.jpg"
}
```

### Detailed Mode
```json
{
  "title": "What I Want",
  "artist": "Morgan Wallen & Tate McRae",
  "album": "I'm The Problem",
  "duration_ms": 184517,
  "duration_formatted": "3:04",
  "genre": "Country",
  "release_date": "2025-05-16T12:00:00Z",
  "explicit": false,
  "track_price": 1.29,
  "currency": "USD",
  "cover_art": {
    "small": "https://...100x100bb.jpg",
    "medium": "https://...300x300bb.jpg",
    "large": "https://...600x600bb.jpg",
    "extra_large": "https://...1000x1000bb.jpg"
  },
  "preview_url": "https://audio-ssl.itunes.apple.com/...",
  "artist_view_url": "https://music.apple.com/us/artist/...",
  "collection_view_url": "https://music.apple.com/us/album/..."
}
```

## 🎛️ Advanced Features

### Metadata Embedding
- **ID3 Tags**: Title, artist, album, track number, genre, year
- **Cover Art**: High-resolution album artwork embedded in MP3
- **Cross-Platform**: Compatible with all major music players

### Playlist Management
- **M3U8 Format**: Standard playlist format supported by most players
- **Relative Paths**: Portable playlists that work when moved
- **Track Duration**: Accurate duration information in playlist

### Error Handling
- **Network Resilience**: Automatic retries and timeouts
- **Missing Tracks**: Graceful handling of unavailable tracks
- **File Safety**: Sanitized filenames for cross-platform compatibility
- **Progress Tracking**: Clear progress indicators and error reporting

## 📁 Project Structure

```
apple_music metadata/
├── script.py              # Apple Music metadata fetcher
├── downloader.py          # YouTube Music downloader
├── setup.py              # Dependency installer
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── demo_cover_art.py    # Cover art demo
├── test_detailed.py     # Testing utilities
└── debug_page.py        # Debug utilities
```

## ⚠️ Important Notes

- **Legal Compliance**: Only download music you have the right to download
- **Rate Limiting**: Built-in delays to respect API limits
- **Quality**: Downloads highest available quality (up to 320kbps MP3)
- **Metadata Accuracy**: Uses official Apple Music metadata for accuracy
- **Cross-Platform**: Works on Windows, macOS, and Linux

## 🎵 Use Cases

1. **Personal Music Library**: Build a high-quality music collection with proper metadata
2. **Playlist Backup**: Create offline backups of your favorite playlists
3. **Music Analysis**: Analyze music trends and metadata
4. **DJ Sets**: Organize music for professional use with proper metadata
5. **Music Discovery**: Explore and download new music with complete information
