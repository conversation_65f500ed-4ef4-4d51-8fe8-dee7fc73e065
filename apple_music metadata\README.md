# Apple Music Metadata Fetcher

A comprehensive Python script for extracting detailed metadata from Apple Music playlists, albums, and tracks, including high-resolution cover art and extensive track information.

## Features

### 🎵 Track Metadata
- **Basic Info**: Title, artist, album, duration, track number
- **Cover Art**: Multiple resolutions (100x100 to 1000x1000px)
- **Audio**: 30-second preview URLs
- **Details**: Genre, release date, explicit content flag
- **Commercial**: Pricing, currency, streaming availability
- **Links**: Apple Music URLs for track, artist, and album

### 🎼 Playlist Support
- **Fast Mode**: Extract track names and basic info quickly
- **Detailed Mode**: Fetch complete metadata for all tracks
- **Comprehensive**: Playlist title, description, curator, cover art

### 📱 Supported URLs
- Playlists: `https://music.apple.com/us/playlist/name/pl.xxxxx`
- Albums: `https://music.apple.com/us/album/name/xxxxx`
- Tracks: `https://music.apple.com/us/song/name/xxxxx`

## Usage

### Basic Mode (Fast)
```bash
python script.py "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
```
- ✅ Extracts all track names, URLs, and durations
- ✅ Fast execution (2-5 seconds)
- ❌ No artist/album info or cover art

### Detailed Mode (Comprehensive)
```bash
python script.py "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12" --detailed
```
- ✅ Complete metadata for all tracks
- ✅ High-resolution cover art URLs
- ✅ Artist, album, genre, pricing info
- ⏱️ Slower execution (10-15 seconds for 100 tracks)

## Example Output

```json
{
  "title": "What I Want",
  "artist": "Morgan Wallen & Tate McRae",
  "album": "I'm The Problem",
  "duration_ms": 184517,
  "duration_formatted": "3:04",
  "genre": "Country",
  "release_date": "2025-05-16T12:00:00Z",
  "explicit": false,
  "track_price": 1.29,
  "currency": "USD",
  "cover_art": {
    "small": "https://...100x100bb.jpg",
    "medium": "https://...300x300bb.jpg", 
    "large": "https://...600x600bb.jpg",
    "extra_large": "https://...1000x1000bb.jpg"
  },
  "preview_url": "https://audio-ssl.itunes.apple.com/...",
  "artist_view_url": "https://music.apple.com/us/artist/...",
  "collection_view_url": "https://music.apple.com/us/album/..."
}
```

## Demo Scripts

### Cover Art Demo
```bash
python demo_cover_art.py
```
- Fetches detailed metadata for a sample track
- Downloads cover art in multiple resolutions
- Shows all available URLs and metadata

### Test Detailed Tracks
```bash
python test_detailed.py
```
- Tests detailed metadata fetching for first 3 tracks
- Useful for debugging and development

## Technical Details

### Data Sources
1. **Apple Music Web Pages**: JSON-LD structured data extraction
2. **iTunes API**: Detailed track metadata and cover art
3. **Web Scraping**: Fallback for missing information

### Cover Art Resolutions
- **Small**: 100x100px (thumbnails)
- **Medium**: 300x300px (lists, cards)
- **Large**: 600x600px (detailed views)
- **Extra Large**: 1000x1000px (high-quality displays)

### Rate Limiting
- 0.1 second delay between iTunes API calls
- Respectful to Apple's servers
- Handles errors gracefully

## Requirements

```bash
pip install requests
```

## Error Handling

- **Network Issues**: Automatic retries and timeouts
- **Encoding Problems**: UTF-8 handling for international characters
- **Missing Data**: Graceful fallbacks and default values
- **API Limits**: Rate limiting and error recovery

## Files

- `script.py` - Main fetcher script
- `demo_cover_art.py` - Cover art demonstration
- `test_detailed.py` - Testing script for detailed mode
- `debug_page.py` - Page structure analysis tool

## Notes

- Works with public Apple Music content
- Requires internet connection
- Cover art URLs are temporary and may expire
- Preview URLs are 30-second samples
- Pricing information is in USD by default

## Example Use Cases

1. **Music Analysis**: Analyze playlist trends and genres
2. **Cover Art Collection**: Download high-quality album artwork
3. **Playlist Backup**: Create detailed backups of playlists
4. **Music Discovery**: Explore related artists and albums
5. **Data Visualization**: Create charts and graphs from music data
