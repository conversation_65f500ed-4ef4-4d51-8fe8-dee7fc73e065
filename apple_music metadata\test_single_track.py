#!/usr/bin/env python3
"""
Test single track download
"""

import sys
from script import AppleMusicFetcher
from downloader import YouTubeMusicDownloader

def test_single_track():
    """Test downloading a single track"""
    url = "https://music.apple.com/us/song/get-the-picture-feat-birdman/1814504208"
    
    print("🧪 Testing single track download...")
    print(f"URL: {url}")
    
    # Initialize components
    fetcher = AppleMusicFetcher()
    downloader = YouTubeMusicDownloader()
    
    # Fetch metadata
    print("\n📋 Fetching metadata...")
    metadata = fetcher.fetch_metadata(url, fetch_track_details=True)
    
    if 'error' in metadata:
        print(f"❌ Error: {metadata['error']}")
        return
    
    print(f"✅ Found track: {metadata['title']} by {metadata['artist']}")
    print(f"Album: {metadata['album']}")
    print(f"Type: {metadata['type']}")
    
    # Test search
    search_query = f"{metadata['artist']} {metadata['title']}"
    print(f"\n🔍 Testing search: {search_query}")
    
    try:
        youtube_url = downloader.search_youtube_music(search_query)
        if youtube_url:
            print(f"✅ Found on YouTube: {youtube_url}")
        else:
            print("❌ Not found on YouTube")
    except Exception as e:
        print(f"❌ Search error: {e}")
    
    print("\n✅ Single track handling test complete!")

if __name__ == "__main__":
    test_single_track()
