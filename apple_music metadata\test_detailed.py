#!/usr/bin/env python3
"""
Test script to fetch detailed metadata for just a few tracks
"""

import sys
import json
from script import AppleMusicFetcher

def test_detailed_tracks():
    """Test detailed track fetching with just a few tracks"""
    url = "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
    
    fetcher = AppleMusicFetcher()
    
    print("Fetching basic playlist metadata...")
    metadata = fetcher.fetch_metadata(url, fetch_track_details=False)
    
    if 'error' in metadata:
        print(f"Error: {metadata['error']}")
        return
    
    print(f"Found {len(metadata['tracks'])} tracks")
    
    # Take just the first 3 tracks for detailed testing
    sample_tracks = metadata['tracks'][:3]
    
    print(f"\nFetching detailed information for {len(sample_tracks)} sample tracks...")
    detailed_tracks = fetcher._enrich_track_details(sample_tracks)
    
    # Create a sample result with detailed tracks
    sample_result = {
        'type': metadata['type'],
        'title': metadata['title'],
        'track_count': len(detailed_tracks),
        'sample_tracks': detailed_tracks
    }
    
    print("\nDetailed track information:")
    try:
        print(json.dumps(sample_result, indent=2, ensure_ascii=False))
    except UnicodeEncodeError:
        print(json.dumps(sample_result, indent=2, ensure_ascii=True))

if __name__ == "__main__":
    test_detailed_tracks()
