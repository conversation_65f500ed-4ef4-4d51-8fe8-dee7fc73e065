#!/usr/bin/env python3
"""
Test the improved search functionality
"""

from downloader import YouTubeMusicDownloader

def test_search_improvements():
    """Test various challenging search queries"""
    downloader = YouTubeMusicDownloader()
    
    test_queries = [
        "<PERSON><PERSON> & SZA luther",  # The one that was failing
        "<PERSON> feat. Post Malone I Had Some Help",  # feat. format
        "Drake ft. Lil Baby Yes Indeed",  # ft. format
        "Taylor Swift featuring Ed <PERSON> End Game",  # featuring format
        "The Weeknd & Ariana Grande Save Your Tears",  # & format
        "Bad Bunny MONACO",  # Single artist, unique title
    ]
    
    print("🧪 Testing improved search functionality...")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Testing: {query}")
        print("-" * 40)
        
        try:
            result = downloader.search_youtube_music(query)
            if result:
                print(f"✅ Success: Found matching track")
            else:
                print(f"❌ Failed: No matching track found")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
    
    print("✅ Search improvement test complete!")

if __name__ == "__main__":
    test_search_improvements()
