#!/usr/bin/env python3
"""
Test the cover art resolution improvements
"""

import requests
from script import AppleMusicFetcher

def get_image_size(url):
    """Get the actual size of an image from URL"""
    try:
        response = requests.head(url, timeout=10)
        if response.status_code == 200:
            # Try to get content length
            content_length = response.headers.get('content-length')
            if content_length:
                size_kb = int(content_length) / 1024
                return f"{size_kb:.1f} KB"
        return "Unknown size"
    except:
        return "Error"

def test_cover_art_resolution():
    """Test cover art resolution for a single track"""
    # Test with a track that should have high-res cover art
    track_url = "https://music.apple.com/us/song/luther/1781270323"
    
    print("🧪 Testing cover art resolution improvements...")
    print(f"URL: {track_url}")
    
    fetcher = AppleMusicFetcher()
    
    # Get track metadata
    print("\n📋 Fetching track metadata...")
    metadata = fetcher.fetch_metadata(track_url, fetch_track_details=True)
    
    if 'error' in metadata:
        print(f"❌ Error: {metadata['error']}")
        return
    
    print(f"✅ Found track: {metadata['title']} by {metadata['artist']}")
    
    # Check cover art URL
    cover_art_url = metadata.get('cover_art')
    if cover_art_url:
        print(f"\n🖼️ Cover Art URL:")
        print(f"URL: {cover_art_url}")
        
        # Extract resolution from URL if possible
        import re
        resolution_match = re.search(r'/(\d+)x(\d+)bb\.', cover_art_url)
        if resolution_match:
            width, height = resolution_match.groups()
            print(f"Resolution: {width}x{height} pixels")
        else:
            print("Resolution: Could not determine from URL")
        
        # Get file size
        file_size = get_image_size(cover_art_url)
        print(f"File size: {file_size}")
        
        # Test if it's actually accessible
        try:
            response = requests.head(cover_art_url, timeout=10)
            if response.status_code == 200:
                print("✅ URL is accessible")
            else:
                print(f"❌ URL returned status: {response.status_code}")
        except Exception as e:
            print(f"❌ URL test failed: {e}")
    else:
        print("❌ No cover art URL found")
    
    # Also test the detailed cover art structure
    print(f"\n🔍 Testing detailed cover art structure...")
    
    # Get iTunes data directly to see all resolutions
    track_id = metadata.get('id')
    if track_id:
        itunes_data = fetcher.get_itunes_lookup_data(track_id, 'track')
        if itunes_data:
            cover_art_data = fetcher._get_track_cover_art(itunes_data)
            if cover_art_data:
                print(f"📊 Available resolutions:")
                for size_name, url in cover_art_data.items():
                    if url:
                        # Extract resolution from URL
                        resolution_match = re.search(r'/(\d+)x(\d+)bb\.', url)
                        if resolution_match:
                            width, height = resolution_match.groups()
                            resolution = f"{width}x{height}"
                        else:
                            resolution = "Unknown"
                        
                        file_size = get_image_size(url)
                        print(f"  {size_name}: {resolution} ({file_size})")
    
    print("\n✅ Cover art resolution test complete!")

if __name__ == "__main__":
    test_cover_art_resolution()
