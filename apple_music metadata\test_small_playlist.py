#!/usr/bin/env python3
"""
Test with a small playlist (just metadata, no download)
"""

from script import AppleMusicFetcher

def test_small_playlist():
    """Test playlist metadata fetching"""
    # Use the same URL but just get metadata
    url = "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
    
    print("🧪 Testing playlist metadata...")
    print(f"URL: {url}")
    
    fetcher = AppleMusicFetcher()
    
    # Get basic metadata (first 3 tracks)
    print("\n📋 Fetching basic metadata...")
    metadata = fetcher.fetch_metadata(url, fetch_track_details=False)
    
    if 'error' in metadata:
        print(f"❌ Error: {metadata['error']}")
        return
    
    print(f"✅ Found playlist: {metadata['title']}")
    print(f"📊 Total tracks: {len(metadata['tracks'])}")
    print(f"🎵 Type: {metadata['type']}")
    
    # Show first 3 tracks
    print(f"\n🎵 First 3 tracks:")
    for i, track in enumerate(metadata['tracks'][:3], 1):
        print(f"  {i}. {track['title']}")
        print(f"     Artist: {track['artist']}")
        print(f"     Album: {track['album']}")
        print(f"     Cover: {'✅' if track.get('cover_art') else '❌'}")
    
    print("\n✅ Playlist metadata test complete!")

if __name__ == "__main__":
    test_small_playlist()
