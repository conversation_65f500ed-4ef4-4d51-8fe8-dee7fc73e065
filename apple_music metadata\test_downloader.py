#!/usr/bin/env python3
"""
Test script for the downloader functionality
"""

import sys
import json
from script import AppleMusicFetcher
from downloader import YouTubeMusicDownloader

def test_metadata_fetching():
    """Test Apple Music metadata fetching"""
    print("🧪 Testing Apple Music metadata fetching...")
    
    # Test URL
    test_url = "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
    
    fetcher = AppleMusicFetcher()
    
    # Test basic mode
    print("  📋 Testing basic mode...")
    metadata = fetcher.fetch_metadata(test_url, fetch_track_details=False)
    
    if 'error' in metadata:
        print(f"  ❌ Basic mode failed: {metadata['error']}")
        return False
    
    print(f"  ✅ Basic mode: Found {len(metadata['tracks'])} tracks")
    
    # Check if tracks have required fields
    sample_track = metadata['tracks'][0] if metadata['tracks'] else {}
    required_fields = ['title', 'artist', 'album', 'cover_art', 'url']
    
    for field in required_fields:
        if field not in sample_track or not sample_track[field]:
            print(f"  ❌ Missing or empty field: {field}")
            return False
    
    print(f"  ✅ Sample track has all required fields")
    print(f"     Title: {sample_track['title']}")
    print(f"     Artist: {sample_track['artist']}")
    print(f"     Album: {sample_track['album']}")
    print(f"     Cover Art: {sample_track['cover_art'][:50]}...")
    
    return True

def test_downloader_setup():
    """Test downloader setup and dependencies"""
    print("\n🧪 Testing downloader setup...")
    
    try:
        downloader = YouTubeMusicDownloader()
        print("  ✅ Downloader initialized successfully")
        return True
    except SystemExit:
        print("  ❌ Downloader setup failed - missing dependencies")
        return False
    except Exception as e:
        print(f"  ❌ Downloader setup error: {e}")
        return False

def test_search_functionality():
    """Test YouTube Music search functionality"""
    print("\n🧪 Testing YouTube Music search...")
    
    try:
        downloader = YouTubeMusicDownloader()
        
        # Test search with a popular song
        test_query = "Morgan Wallen What I Want"
        print(f"  🔍 Searching for: {test_query}")
        
        result = downloader.search_youtube_music(test_query)
        
        if result:
            print(f"  ✅ Search successful: {result}")
            return True
        else:
            print(f"  ❌ Search failed - no results")
            return False
            
    except Exception as e:
        print(f"  ❌ Search error: {e}")
        return False

def test_filename_sanitization():
    """Test filename sanitization"""
    print("\n🧪 Testing filename sanitization...")
    
    downloader = YouTubeMusicDownloader()
    
    test_cases = [
        ("Normal Song Title", "Normal Song Title"),
        ("Song/With\\Invalid:Chars", "Song_With_Invalid_Chars"),
        ("Song   With   Multiple   Spaces", "Song With Multiple Spaces"),
        ("Song<>:|?*\"", "Song_______"),
        ("A" * 250, "A" * 200)  # Test length limit
    ]
    
    for input_name, expected_pattern in test_cases:
        result = downloader.sanitize_filename(input_name)
        print(f"  Input: '{input_name[:30]}{'...' if len(input_name) > 30 else ''}'")
        print(f"  Output: '{result}'")
        
        # Basic checks
        if len(result) > 200:
            print(f"  ❌ Result too long: {len(result)} chars")
            return False
        
        invalid_chars = '<>:"/\\|?*'
        if any(char in result for char in invalid_chars):
            print(f"  ❌ Result contains invalid characters")
            return False
    
    print("  ✅ Filename sanitization working correctly")
    return True

def main():
    """Run all tests"""
    print("🧪 Apple Music to YouTube Music Downloader Test Suite")
    print("=" * 60)
    
    tests = [
        ("Metadata Fetching", test_metadata_fetching),
        ("Downloader Setup", test_downloader_setup),
        ("Search Functionality", test_search_functionality),
        ("Filename Sanitization", test_filename_sanitization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The downloader is ready to use.")
        print("\n📋 Next steps:")
        print("1. Run: python downloader.py '<apple_music_playlist_url>'")
        print("2. Example: python downloader.py 'https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12'")
    else:
        print("⚠️ Some tests failed. Please check the setup and dependencies.")
        print("💡 Try running: python setup.py")

if __name__ == "__main__":
    main()
