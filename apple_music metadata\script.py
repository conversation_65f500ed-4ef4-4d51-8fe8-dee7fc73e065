#!/usr/bin/env python3
"""
Apple Music Metadata Fetcher
Fetches metadata for tracks, albums, and playlists from Apple Music URLs
"""

import re
import requests
import json
import sys
from urllib.parse import urlparse, parse_qs
from typing import Dict, List, Optional, Union

class AppleMusicFetcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def extract_id_from_url(self, url: str) -> tuple:
        """Extract content type and ID from Apple Music URL"""
        try:
            # Handle different URL formats
            # Playlists: music.apple.com/[country]/playlist/[name]/pl.[id]
            # Albums/Songs: music.apple.com/[country]/[type]/[name]/[numeric_id]

            # First check for playlist format with pl. prefix
            playlist_patterns = [
                r'music\.apple\.com/[a-z]{2}/playlist/[^/]+/(pl\.[a-zA-Z0-9]+)',  # With country
                r'music\.apple\.com/playlist/[^/]+/(pl\.[a-zA-Z0-9]+)',           # Without country
                r'apple\.com/[a-z]{2}/playlist/[^/]+/(pl\.[a-zA-Z0-9]+)',        # Alt domain with country
                r'apple\.com/playlist/[^/]+/(pl\.[a-zA-Z0-9]+)',                 # Alt domain without country
            ]

            for pattern in playlist_patterns:
                match = re.search(pattern, url)
                if match:
                    return 'playlist', match.group(1)

            # Then check for regular format with numeric IDs
            regular_patterns = [
                r'music\.apple\.com/[a-z]{2}/([^/]+)/[^/]+/(\d+)',  # With country code
                r'music\.apple\.com/([^/]+)/[^/]+/(\d+)',           # Without country code
                r'apple\.com/[a-z]{2}/([^/]+)/[^/]+/(\d+)',        # Alternative domain with country
                r'apple\.com/([^/]+)/[^/]+/(\d+)',                 # Alternative domain without country
            ]

            for pattern in regular_patterns:
                match = re.search(pattern, url)
                if match:
                    if len(match.groups()) == 2:
                        content_type = match.group(1)
                        content_id = match.group(2)
                    else:
                        content_type = match.group(1)
                        content_id = match.group(2)

                    # Map content types
                    type_mapping = {
                        'song': 'track',
                        'album': 'album',
                        'playlist': 'playlist'
                    }

                    mapped_type = type_mapping.get(content_type, content_type)
                    return mapped_type, content_id

            # Fallback: try to extract ID from the end of the URL
            # Handle both playlist IDs (pl.xxx) and numeric IDs
            fallback_patterns = [
                r'/(pl\.[a-zA-Z0-9]+)(?:\?|$)',  # Playlist ID
                r'/(\d+)(?:\?|$)'                # Numeric ID
            ]

            for pattern in fallback_patterns:
                id_match = re.search(pattern, url)
                if id_match:
                    content_id = id_match.group(1)

                    # Determine type from URL structure
                    if '/playlist/' in url or content_id.startswith('pl.'):
                        return 'playlist', content_id
                    elif '/album/' in url:
                        return 'album', content_id
                    elif '/song/' in url:
                        return 'track', content_id
                    else:
                        # Default based on ID format
                        if content_id.startswith('pl.'):
                            return 'playlist', content_id
                        else:
                            return 'album', content_id

            raise ValueError("Could not parse Apple Music URL")

        except Exception as e:
            raise ValueError(f"Invalid Apple Music URL: {e}")

    def get_itunes_lookup_data(self, content_id: str, content_type: str) -> Dict:
        """Get basic data from iTunes lookup API"""
        try:
            # Skip iTunes API for playlist IDs with pl. prefix as they're not supported
            if content_type == 'playlist' and content_id.startswith('pl.'):
                return {}

            entity_map = {
                'track': 'song',
                'album': 'album',
                'playlist': 'mix'
            }

            entity = entity_map.get(content_type, 'song')
            url = f"https://itunes.apple.com/lookup?id={content_id}&entity={entity}"

            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            data = response.json()
            if not data.get('results'):
                raise ValueError("No results found")

            return data['results'][0]

        except Exception as e:
            print(f"Warning: iTunes lookup failed: {e}")
            return {}

    def scrape_apple_music_page(self, url: str) -> Dict:
        """Scrape Apple Music web page for additional metadata"""
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            html = response.text

            # Extract JSON-LD data
            json_ld_pattern = r'<script type="application/ld\+json">(.*?)</script>'
            json_matches = re.findall(json_ld_pattern, html, re.DOTALL)

            metadata = {}

            for json_str in json_matches:
                try:
                    data = json.loads(json_str.strip())
                    if isinstance(data, dict):
                        metadata.update(data)
                    elif isinstance(data, list):
                        for item in data:
                            if isinstance(item, dict):
                                metadata.update(item)
                except json.JSONDecodeError:
                    continue

            # Extract Open Graph metadata
            og_patterns = {
                'title': r'<meta property="og:title" content="([^"]*)"',
                'description': r'<meta property="og:description" content="([^"]*)"',
                'image': r'<meta property="og:image" content="([^"]*)"',
                'url': r'<meta property="og:url" content="([^"]*)"'
            }

            for key, pattern in og_patterns.items():
                match = re.search(pattern, html)
                if match:
                    metadata[f'og_{key}'] = match.group(1)

            # Extract Twitter card metadata
            twitter_patterns = {
                'title': r'<meta name="twitter:title" content="([^"]*)"',
                'description': r'<meta name="twitter:description" content="([^"]*)"',
                'image': r'<meta name="twitter:image" content="([^"]*)"'
            }

            for key, pattern in twitter_patterns.items():
                match = re.search(pattern, html)
                if match:
                    metadata[f'twitter_{key}'] = match.group(1)

            # Extract playlist tracks if this is a playlist page
            if '/playlist/' in url:
                tracks = self._extract_playlist_tracks(html)
                if tracks:
                    metadata['playlist_tracks'] = tracks

            return metadata

        except Exception as e:
            print(f"Warning: Web scraping failed: {e}")
            return {}

    def _extract_playlist_tracks(self, html: str) -> List[Dict]:
        """Extract track information from playlist HTML"""
        tracks = []
        print("[DEBUG] _extract_playlist_tracks: Starting playlist track extraction...")

        try:
            # Method 1: Look for JSON data in script tags
            print("[DEBUG] _extract_playlist_tracks: Attempting Method 1: Extract from script tags...")
            script_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__NUXT__\s*=\s*({.*?});',
                r'__APOLLO_STATE__\s*=\s*({.*?});'
            ]

            found_script_json_matches = False
            for pattern_idx, pattern in enumerate(script_patterns):
                print(f"[DEBUG] _extract_playlist_tracks: M1: Trying pattern #{pattern_idx + 1}: {pattern[:50]}...")
                matches = re.findall(pattern, html, re.DOTALL)
                if matches:
                    print(f"[DEBUG] _extract_playlist_tracks: M1: Found {len(matches)} raw match(es) for pattern #{pattern_idx + 1}.")
                    found_script_json_matches = True
                    for match_idx, match_str in enumerate(matches):
                        print(f"[DEBUG] _extract_playlist_tracks: M1: Processing raw match #{match_idx + 1} (length: {len(match_str)} chars)")
                        try:
                            data = json.loads(match_str)
                            print("[DEBUG] _extract_playlist_tracks: M1: Successfully parsed raw match as JSON.")
                            extracted_tracks_from_json = self._parse_tracks_from_json(data) # Call to helper
                            if extracted_tracks_from_json:
                                print(f"[DEBUG] _extract_playlist_tracks: M1: _parse_tracks_from_json returned {len(extracted_tracks_from_json)} tracks.")
                                tracks.extend(extracted_tracks_from_json)
                            # else: # Already logged in _parse_tracks_from_json
                                # print("[DEBUG] _extract_playlist_tracks: M1: _parse_tracks_from_json returned no tracks from this JSON blob.")
                        except (json.JSONDecodeError, Exception) as e_json:
                            print(f"[DEBUG] _extract_playlist_tracks: M1: Error parsing script JSON match: {e_json}")
                            continue
                else:
                    print(f"[DEBUG] _extract_playlist_tracks: M1: No matches for pattern #{pattern_idx + 1}.")

            if not found_script_json_matches:
                 print("[DEBUG] _extract_playlist_tracks: M1: No script JSON patterns yielded any raw matches.")

            if tracks:
                print(f"[DEBUG] _extract_playlist_tracks: M1: Found {len(tracks)} tracks after Method 1.")

            # Method 2: Look for structured data
            if not tracks:
                print("[DEBUG] _extract_playlist_tracks: Attempting Method 2: Extract from structured data (JSON-LD)...")
                extracted_structured_tracks = self._extract_tracks_from_structured_data(html) # Call to helper
                if extracted_structured_tracks:
                    # print(f"[DEBUG] _extract_playlist_tracks: M2: _extract_tracks_from_structured_data returned {len(extracted_structured_tracks)} tracks.") # Logged in helper
                    tracks.extend(extracted_structured_tracks)
                    print(f"[DEBUG] _extract_playlist_tracks: M2: Total {len(tracks)} tracks after Method 2.")
                # else: # Already logged in _extract_tracks_from_structured_data
                    # print("[DEBUG] _extract_playlist_tracks: M2: _extract_tracks_from_structured_data returned no tracks.")

            # Method 3: Fallback - parse visible track elements
            if not tracks:
                print("[DEBUG] _extract_playlist_tracks: Attempting Method 3: Extract from DOM elements...")
                extracted_dom_tracks = self._extract_tracks_from_dom(html) # Call to helper
                if extracted_dom_tracks:
                    # print(f"[DEBUG] _extract_playlist_tracks: M3: _extract_tracks_from_dom returned {len(extracted_dom_tracks)} tracks.") # Logged in helper
                    tracks.extend(extracted_dom_tracks)
                    print(f"[DEBUG] _extract_playlist_tracks: M3: Total {len(tracks)} tracks after Method 3.")
                # else: # Already logged in _extract_tracks_from_dom
                    # print("[DEBUG] _extract_playlist_tracks: M3: _extract_tracks_from_dom returned no tracks.")

        except Exception as e:
            print(f"[DEBUG] _extract_playlist_tracks: Warning: Overall track extraction process failed: {e}")

        print(f"[DEBUG] _extract_playlist_tracks: Finished extraction. Total tracks found: {len(tracks)}.")
        return tracks

    def _parse_tracks_from_json(self, data: Dict) -> List[Dict]:
        """Parse tracks from embedded JSON data"""
        tracks = []
        print("[DEBUG] _parse_tracks_from_json: Starting to parse provided JSON data...")

        # Limit recursion depth to avoid potential issues with very deep JSON
        recursion_limit = 20

        def find_tracks_recursive(obj, current_depth, path=""):
            if current_depth > recursion_limit:
                print(f"[DEBUG] _parse_tracks_from_json: Recursion limit ({recursion_limit}) reached at path: {path}")
                return

            if isinstance(obj, dict):
                if self._is_track_object(obj):
                    # print(f"[DEBUG] _parse_tracks_from_json: Found potential track object at path: {path}")
                    track_info = self._extract_track_info(obj)
                    if track_info:
                        # print(f"[DEBUG] _parse_tracks_from_json: Successfully extracted track: {track_info.get('title')}")
                        tracks.append(track_info)
                    # else:
                        # print(f"[DEBUG] _parse_tracks_from_json: _is_track_object was true, but _extract_track_info failed for path: {path}")

                for key, value in obj.items():
                    if isinstance(value, (dict, list)):
                        find_tracks_recursive(value, current_depth + 1, f"{path}.{key}" if path else key)

            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        find_tracks_recursive(item, current_depth + 1, f"{path}[{i}]" if path else f"[{i}]")

        find_tracks_recursive(data, 0)
        if not tracks:
            print("[DEBUG] _parse_tracks_from_json: No tracks found in the provided JSON data after full traversal.")
        else:
            print(f"[DEBUG] _parse_tracks_from_json: Finished parsing. Found {len(tracks)} tracks in JSON.")
        return tracks

    def _is_track_object(self, obj: Dict) -> bool:
        """Check if an object represents a track"""
        track_indicators = [
            'trackName', 'songName', 'title', 'name',
            'artistName', 'artist', 'albumName', 'album',
            'duration', 'durationInMillis', 'playTimeMs'
        ]

        # Object should have at least track name and artist
        has_name = any(key in obj for key in ['trackName', 'songName', 'title', 'name'])
        has_artist = any(key in obj for key in ['artistName', 'artist'])

        return has_name and has_artist

    def _extract_track_info(self, obj: Dict) -> Optional[Dict]:
        """Extract standardized track info from a track object"""
        try:
            # Try different possible field names
            title = (obj.get('trackName') or obj.get('songName') or
                    obj.get('title') or obj.get('name', '')).strip()

            artist = (obj.get('artistName') or obj.get('artist') or
                     obj.get('primaryArtist', {}).get('name', '')).strip()

            album = (obj.get('albumName') or obj.get('album') or
                    obj.get('collectionName', '')).strip()

            # Duration handling
            duration_ms = (obj.get('durationInMillis') or obj.get('playTimeMs') or
                          obj.get('duration') or obj.get('trackTimeMillis'))

            if not title or not artist:
                return None

            track_info = {
                'title': title,
                'artist': artist,
                'album': album if album else 'Unknown Album',
                'duration_ms': duration_ms,
                'track_number': obj.get('trackNumber'),
                'explicit': obj.get('contentRating') == 'explicit' or obj.get('trackExplicitness') == 'explicit',
                'preview_url': obj.get('previewUrl'),
                'isrc': obj.get('isrc'),
                'apple_music_id': obj.get('trackId') or obj.get('id')
            }

            return track_info

        except Exception:
            return None

    def _extract_tracks_from_structured_data(self, html: str) -> List[Dict]:
        """Extract tracks from structured data/microdata (JSON-LD)"""
        tracks = []
        print("[DEBUG] _extract_tracks_from_structured_data: Starting extraction...")

        try:
            # Updated pattern to handle script tags with id attributes and other variations
            json_ld_patterns = [
                r'<script[^>]*type="application/ld\+json"[^>]*>(.*?)</script>',  # More flexible pattern
                r'<script type="application/ld\+json"[^>]*>(.*?)</script>',      # Original pattern
                r'<script id="[^"]*"[^>]*type="application/ld\+json"[^>]*>(.*?)</script>'  # With id attribute
            ]

            json_matches = []
            for pattern in json_ld_patterns:
                matches = re.findall(pattern, html, re.DOTALL)
                json_matches.extend(matches)

            if not json_matches:
                print("[DEBUG] _extract_tracks_from_structured_data: No JSON-LD script tags found.")
                return tracks

            print(f"[DEBUG] _extract_tracks_from_structured_data: Found {len(json_matches)} JSON-LD script tag(s).")
            for i, json_str in enumerate(json_matches):
                print(f"[DEBUG] _extract_tracks_from_structured_data: Processing JSON-LD blob #{i+1} (length: {len(json_str)} chars)")
                try:
                    # Clean the JSON string to handle encoding issues
                    cleaned_json = json_str.strip()
                    # Replace problematic characters that might cause encoding issues
                    cleaned_json = cleaned_json.encode('utf-8', errors='ignore').decode('utf-8')

                    data = json.loads(cleaned_json)
                    items_to_check = data if isinstance(data, list) else [data]

                    for item_idx, item in enumerate(items_to_check):
                        # print(f"[DEBUG] _extract_tracks_from_structured_data: Checking item #{item_idx+1} in JSON-LD blob. Type: {item.get('@type')}")
                        if item.get('@type') == 'MusicPlaylist':
                            print("[DEBUG] _extract_tracks_from_structured_data: Found 'MusicPlaylist' item.")
                            playlist_tracks_data = item.get('track', [])
                            if not isinstance(playlist_tracks_data, list): # Ensure 'track' is a list
                                print(f"[DEBUG] _extract_tracks_from_structured_data: 'track' property is not a list, but {type(playlist_tracks_data)}. Skipping.")
                                continue

                            print(f"[DEBUG] _extract_tracks_from_structured_data: 'MusicPlaylist' has {len(playlist_tracks_data)} items in 'track' property.")
                            for track_idx, track_item_data in enumerate(playlist_tracks_data):
                                if isinstance(track_item_data, dict):
                                    # print(f"[DEBUG] _extract_tracks_from_structured_data: Processing track item #{track_idx+1} from 'MusicPlaylist'.")

                                    # Extract artist from byArtist or try to get it from the URL/other sources
                                    artist_name = ''
                                    if 'byArtist' in track_item_data:
                                        if isinstance(track_item_data['byArtist'], dict):
                                            artist_name = track_item_data['byArtist'].get('name', '')
                                        elif isinstance(track_item_data['byArtist'], str):
                                            artist_name = track_item_data['byArtist']

                                    # Extract album from inAlbum
                                    album_name = ''
                                    if 'inAlbum' in track_item_data:
                                        if isinstance(track_item_data['inAlbum'], dict):
                                            album_name = track_item_data['inAlbum'].get('name', '')
                                        elif isinstance(track_item_data['inAlbum'], str):
                                            album_name = track_item_data['inAlbum']

                                    track_info = {
                                        'title': track_item_data.get('name', ''),
                                        'artist': artist_name,
                                        'album': album_name,
                                        'duration_ms': self._parse_duration(track_item_data.get('duration')),
                                        'url': track_item_data.get('url'),
                                        'track_number': track_idx + 1  # Add track number based on position
                                    }

                                    # For Apple Music playlists, we often only have track name and URL
                                    # Accept tracks even if artist is missing since we can get it later
                                    if track_info['title']:
                                        print(f"[DEBUG] _extract_tracks_from_structured_data: Extracted track: {track_info['title']}")
                                        tracks.append(track_info)
                                    else:
                                        print(f"[DEBUG] _extract_tracks_from_structured_data: Missing title for track item #{track_idx+1}")
                                # else:
                                    # print(f"[DEBUG] _extract_tracks_from_structured_data: Track item in 'MusicPlaylist' is not a dict: {type(track_item_data)}")
                        # elif item.get('@type') == 'ItemList': # Example: Could check for other types like ItemList of MusicRecordings
                            # print(f"[DEBUG] _extract_tracks_from_structured_data: Found 'ItemList'. Items: {len(item.get('itemListElement', []))}")
                            # pass # Add logic for ItemList if needed

                except (json.JSONDecodeError, Exception) as e_json_ld:
                    print(f"[DEBUG] _extract_tracks_from_structured_data: Error parsing JSON-LD blob #{i+1}: {e_json_ld}")
                    continue

        except Exception as e:
            print(f"[DEBUG] _extract_tracks_from_structured_data: Overall warning: {e}")

        if not tracks:
            print("[DEBUG] _extract_tracks_from_structured_data: No tracks extracted using this method.")
        else:
            print(f"[DEBUG] _extract_tracks_from_structured_data: Finished. Found {len(tracks)} tracks.")
        return tracks

    def _extract_tracks_from_dom(self, html: str) -> List[Dict]:
        """Fallback method to extract tracks from DOM elements"""
        tracks = []
        print("[DEBUG] _extract_tracks_from_dom: Starting DOM extraction (fallback)...")

        try:
            # These patterns are very generic and likely to break or be inaccurate.
            # Consider using a proper HTML parsing library like BeautifulSoup if this method is critical.
            track_patterns = [
                r'<div[^>]*data-testid="track-list-item"[^>]*>(.*?)</div>', # Common in React apps
                r'<tr[^>]*class="[^"]*track[^"]*"[^>]*>(.*?)</tr>',
                r'<li[^>]*class="[^"]*song[^"]*"[^>]*>(.*?)</li>',
                # Add more specific patterns if known structures exist
            ]

            found_dom_matches = False
            for pattern_idx, pattern in enumerate(track_patterns):
                print(f"[DEBUG] _extract_tracks_from_dom: Trying pattern #{pattern_idx + 1}: {pattern[:50]}...")
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    found_dom_matches = True
                    print(f"[DEBUG] _extract_tracks_from_dom: Found {len(matches)} raw match(es) for pattern #{pattern_idx + 1}.")
                    for match_idx, match_str in enumerate(matches):
                        # print(f"[DEBUG] _extract_tracks_from_dom: Processing DOM match #{match_idx + 1}")
                        # Attempt to extract title and artist from the matched HTML snippet
                        # These are very naive regexes for HTML parsing
                        title_match = re.search(r'<span[^>]*class="[^"]*(?:track-name|song-title|title)[^"]*"[^>]*>([^<]+)</span>', match_str, re.IGNORECASE)
                        if not title_match: # Fallback to a more generic span
                            title_match = re.search(r'<div[^>]*class="[^"]*(?:track-name|song-title|title)[^"]*"[^>]*>([^<]+)</div>', match_str, re.IGNORECASE)
                        if not title_match: # Fallback to a very generic span if others fail
                             title_match = re.search(r'<span[^>]*>([^<]+)</span>', match_str)


                        artist_match = re.search(r'<span[^>]*class="[^"]*(?:artist-name|track-artist|artist)[^"]*"[^>]*>([^<]+)</span>', match_str, re.IGNORECASE)
                        if not artist_match: # Fallback
                            artist_match = re.search(r'<a[^>]*href="[^"]*/artist/[^"]*"[^>]*>([^<]+)</a>', match_str, re.IGNORECASE)
                        if not artist_match: # Fallback to "by Artist" pattern
                            artist_match = re.search(r'by\s+([^<]+)', match_str)

                        if title_match:
                            title = title_match.group(1).strip()
                            artist = artist_match.group(1).strip() if artist_match else 'Unknown Artist (DOM)'

                            # print(f"[DEBUG] _extract_tracks_from_dom: Extracted Title: '{title}', Artist: '{artist}'")
                            tracks.append({
                                'title': title,
                                'artist': artist,
                                'album': 'Unknown Album (DOM)',
                                'duration_ms': None, # DOM parsing rarely gets duration easily
                                'track_number': len(tracks) + 1
                            })
                        # else:
                            # print(f"[DEBUG] _extract_tracks_from_dom: Could not extract title from DOM match: {match_str[:100]}...")
                else:
                    print(f"[DEBUG] _extract_tracks_from_dom: No matches for pattern #{pattern_idx + 1}.")

            if not found_dom_matches:
                print("[DEBUG] _extract_tracks_from_dom: No DOM patterns yielded any raw matches.")

        except Exception as e:
            print(f"[DEBUG] _extract_tracks_from_dom: Warning: {e}")

        if not tracks:
            print("[DEBUG] _extract_tracks_from_dom: No tracks extracted using this method.")
        else:
            print(f"[DEBUG] _extract_tracks_from_dom: Finished. Found {len(tracks)} tracks.")
        return tracks

    def _parse_duration(self, duration_str: str) -> Optional[int]:
        """Parse duration string to milliseconds"""
        if not duration_str:
            return None

        try:
            # Handle ISO 8601 duration format (PT3M45S)
            if duration_str.startswith('PT'):
                import re # Moved import inside function as it's only used here
                minutes_match = re.search(r'(\d+)M', duration_str) # Renamed 'minutes' to 'minutes_match'
                seconds_match = re.search(r'(\d+)S', duration_str) # Renamed 'seconds' to 'seconds_match'

                total_seconds = 0
                if minutes_match:
                    total_seconds += int(minutes_match.group(1)) * 60
                if seconds_match:
                    total_seconds += int(seconds_match.group(1))

                return total_seconds * 1000

            # Handle MM:SS format
            elif ':' in duration_str:
                parts = duration_str.split(':')
                if len(parts) == 2:
                    minutes_val, seconds_val = parts # Renamed 'minutes' and 'seconds'
                    return (int(minutes_val) * 60 + int(seconds_val)) * 1000

        except Exception:
            pass

        return None

    def fetch_track_metadata(self, track_id: str, url: str) -> Dict:
        """Fetch metadata for a single track"""
        # Get iTunes data
        itunes_data = self.get_itunes_lookup_data(track_id, 'track')

        # Get web scraped data
        web_data = self.scrape_apple_music_page(url)

        # Combine and prioritize data
        metadata = {
            'type': 'track',
            'id': track_id,
            'url': url,
            'title': (web_data.get('og_title') or web_data.get('twitter_title') or
                     itunes_data.get('trackName') or 'Unknown Track'),
            'artist': itunes_data.get('artistName', 'Unknown Artist'),
            'album': itunes_data.get('collectionName', 'Unknown Album'),
            'genre': itunes_data.get('primaryGenreName'),
            'release_date': itunes_data.get('releaseDate'),
            'duration_ms': itunes_data.get('trackTimeMillis'),
            'track_number': itunes_data.get('trackNumber'),
            'cover_art': self._get_best_cover_art(itunes_data, web_data),
            'preview_url': itunes_data.get('previewUrl'),
            'explicit': itunes_data.get('trackExplicitness') == 'explicit'
        }

        return metadata

    def fetch_album_metadata(self, album_id: str, url: str) -> Dict:
        """Fetch metadata for an album"""
        # Get iTunes data
        itunes_data = self.get_itunes_lookup_data(album_id, 'album')

        # Get web scraped data
        web_data = self.scrape_apple_music_page(url)

        # Get album tracks
        album_tracks = [] # Renamed 'tracks' to 'album_tracks'
        try:
            tracks_url = f"https://itunes.apple.com/lookup?id={album_id}&entity=song"
            response = self.session.get(tracks_url, timeout=10)
            tracks_data = response.json()
            album_tracks = [track_item for track_item in tracks_data.get('results', [])  # Renamed 'track' to 'track_item'
                     if track_item.get('wrapperType') == 'track']
        except:
            album_tracks = [] # Ensure it's initialized even on error

        metadata = {
            'type': 'album',
            'id': album_id,
            'url': url,
            'title': (web_data.get('og_title') or web_data.get('twitter_title') or
                     itunes_data.get('collectionName') or 'Unknown Album'),
            'artist': itunes_data.get('artistName', 'Unknown Artist'),
            'genre': itunes_data.get('primaryGenreName'),
            'release_date': itunes_data.get('releaseDate'),
            'track_count': itunes_data.get('trackCount', len(album_tracks)),
            'cover_art': self._get_best_cover_art(itunes_data, web_data),
            'copyright': itunes_data.get('copyright'),
            'tracks': [
                {
                    'title': track_item.get('trackName'), # Renamed 'track' to 'track_item'
                    'track_number': track_item.get('trackNumber'),
                    'duration_ms': track_item.get('trackTimeMillis'),
                    'preview_url': track_item.get('previewUrl')
                }
                for track_item in album_tracks # Renamed 'track' to 'track_item'
            ]
        }

        return metadata

    def fetch_playlist_metadata(self, playlist_id: str, url: str, fetch_track_details: bool = False) -> Dict:
        """Fetch metadata for a playlist"""
        # Playlists are harder to get via API, rely heavily on web scraping
        web_data = self.scrape_apple_music_page(url)

        # Try iTunes lookup as fallback
        itunes_data = self.get_itunes_lookup_data(playlist_id, 'playlist')

        # Extract playlist name from various sources
        playlist_name_val = ( # Renamed 'playlist_name' to 'playlist_name_val'
            web_data.get('og_title') or
            web_data.get('twitter_title') or
            web_data.get('name') or
            itunes_data.get('playlistName') or
            'Unknown Playlist'
        )

        # Clean up playlist name (remove " - playlist by Apple Music" etc.)
        playlist_name_val = re.sub(r'\s*-\s*playlist\s*by.*', '', playlist_name_val, flags=re.IGNORECASE)
        playlist_name_val = re.sub(r'\s*\|\s*Apple Music.*', '', playlist_name_val, flags=re.IGNORECASE) # Added missing quote and flags

        # Extract tracks from web_data if available
        playlist_tracks_list = web_data.get('playlist_tracks', []) # Renamed 'tracks' to 'playlist_tracks_list'

        # Optionally fetch detailed track information
        if fetch_track_details and playlist_tracks_list:
            print(f"Fetching detailed information for {len(playlist_tracks_list)} tracks...")
            playlist_tracks_list = self._enrich_track_details(playlist_tracks_list)

        metadata = {
            'type': 'playlist',
            'id': playlist_id,
            'url': url,
            'title': playlist_name_val,
            'description': web_data.get('og_description') or web_data.get('description'),
            'curator': self._extract_curator(web_data, itunes_data),
            'cover_art': self._get_best_cover_art(itunes_data, web_data),
            'track_count': len(playlist_tracks_list),
            'tracks': playlist_tracks_list
        }

        return metadata

    def _get_best_cover_art(self, itunes_data: Dict, web_data: Dict) -> str:
        """Get the highest quality cover art URL"""
        # Priority order: web scraped images, then iTunes
        candidates = [
            web_data.get('og_image'),
            web_data.get('twitter_image'),
            itunes_data.get('artworkUrl100'),
            itunes_data.get('artworkUrl60')
        ]

        for candidate_url in candidates: # Renamed 'url' to 'candidate_url'
            if candidate_url:
                # Try to get higher resolution version
                if 'itunes.apple.com' in candidate_url:
                    # Replace size parameter with larger size
                    candidate_url = re.sub(r'/\d+x\d+bb\.', '/1000x1000bb.', candidate_url)
                return candidate_url

        return None

    def _extract_curator(self, web_data: Dict, itunes_data: Dict) -> str:
        """Extract playlist curator/creator"""
        # Look for curator info in various places
        title_val = web_data.get('og_title', '') # Renamed 'title' to 'title_val'
        description_val = web_data.get('og_description', '') # Renamed 'description' to 'description_val'

        # Check if it's an Apple Music official playlist
        if 'Apple Music' in title_val or 'Apple Music' in description_val:
            return 'Apple Music'

        # Try to extract from title patterns like "Playlist Name by Artist"
        by_match = re.search(r'by\s+([^|]+)', title_val)
        if by_match:
            return by_match.group(1).strip()

        return itunes_data.get('artistName', 'Unknown')

    def _enrich_track_details(self, tracks: List[Dict]) -> List[Dict]:
        """Enrich track details by fetching additional information from iTunes API"""
        enriched_tracks = []

        for i, track in enumerate(tracks):
            print(f"Fetching details for track {i+1}/{len(tracks)}: {track.get('title', 'Unknown')}")

            # Extract track ID from Apple Music URL
            track_url = track.get('url', '')
            track_id = None

            if track_url:
                # Extract numeric ID from URL like https://music.apple.com/us/song/track-name/1234567890
                id_match = re.search(r'/(\d+)(?:\?|$)', track_url)
                if id_match:
                    track_id = id_match.group(1)

            # Try to get detailed info from iTunes API
            if track_id:
                try:
                    itunes_data = self.get_itunes_lookup_data(track_id, 'track')
                    if itunes_data:
                        # Update track with iTunes data
                        track['artist'] = itunes_data.get('artistName', track.get('artist', ''))
                        track['album'] = itunes_data.get('collectionName', track.get('album', ''))
                        track['genre'] = itunes_data.get('primaryGenreName')
                        track['release_date'] = itunes_data.get('releaseDate')
                        track['explicit'] = itunes_data.get('trackExplicitness') == 'explicit'
                        track['preview_url'] = itunes_data.get('previewUrl')
                        track['apple_music_id'] = track_id

                        # Add cover art URLs in multiple resolutions
                        track['cover_art'] = self._get_track_cover_art(itunes_data)

                        # Add additional metadata
                        track['artist_id'] = itunes_data.get('artistId')
                        track['collection_id'] = itunes_data.get('collectionId')
                        track['track_price'] = itunes_data.get('trackPrice')
                        track['collection_price'] = itunes_data.get('collectionPrice')
                        track['currency'] = itunes_data.get('currency')
                        track['country'] = itunes_data.get('country')
                        track['disc_number'] = itunes_data.get('discNumber')
                        track['track_count'] = itunes_data.get('trackCount')
                        track['disc_count'] = itunes_data.get('discCount')
                        track['content_advisory_rating'] = itunes_data.get('contentAdvisoryRating')
                        track['is_streamable'] = itunes_data.get('isStreamable')

                        # Add artist and album URLs
                        track['artist_view_url'] = itunes_data.get('artistViewUrl')
                        track['collection_view_url'] = itunes_data.get('collectionViewUrl')

                        # Use iTunes duration if we don't have it or if it's more accurate
                        if not track.get('duration_ms') or itunes_data.get('trackTimeMillis'):
                            track['duration_ms'] = itunes_data.get('trackTimeMillis')

                        # Add human-readable duration
                        if track.get('duration_ms'):
                            track['duration_formatted'] = self._format_duration(track['duration_ms'])

                except Exception as e:
                    print(f"Warning: Could not fetch details for track {track.get('title')}: {e}")

            enriched_tracks.append(track)

            # Add a small delay to be respectful to the API
            import time
            time.sleep(0.1)

        return enriched_tracks

    def _get_track_cover_art(self, itunes_data: Dict) -> Dict:
        """Extract cover art URLs in multiple resolutions from iTunes data"""
        cover_art = {}

        # Get the base artwork URL (usually 100x100)
        base_url = itunes_data.get('artworkUrl100')
        if not base_url:
            base_url = itunes_data.get('artworkUrl60')

        if base_url:
            # Generate different resolutions by replacing the size parameter
            # iTunes artwork URLs follow the pattern: .../image.jpg/100x100bb.jpg
            cover_art = {
                'small': re.sub(r'/\d+x\d+bb\.', '/100x100bb.', base_url),      # 100x100
                'medium': re.sub(r'/\d+x\d+bb\.', '/300x300bb.', base_url),     # 300x300
                'large': re.sub(r'/\d+x\d+bb\.', '/600x600bb.', base_url),      # 600x600
                'extra_large': re.sub(r'/\d+x\d+bb\.', '/1000x1000bb.', base_url), # 1000x1000
                'original': base_url  # Keep original size
            }

        return cover_art

    def _format_duration(self, duration_ms: int) -> str:
        """Format duration from milliseconds to MM:SS format"""
        if not duration_ms:
            return "0:00"

        total_seconds = duration_ms // 1000
        minutes = total_seconds // 60
        seconds = total_seconds % 60

        return f"{minutes}:{seconds:02d}"

    def fetch_metadata(self, url: str, fetch_track_details: bool = False) -> Dict:
        """Main method to fetch metadata for any Apple Music URL"""
        try:
            content_type, content_id = self.extract_id_from_url(url)

            if content_type == 'track':
                return self.fetch_track_metadata(content_id, url)
            elif content_type == 'album':
                return self.fetch_album_metadata(content_id, url)
            elif content_type == 'playlist':
                return self.fetch_playlist_metadata(content_id, url, fetch_track_details)
            else:
                raise ValueError(f"Unsupported content type: {content_type}")

        except Exception as e:
            return {
                'error': str(e),
                'url': url
            }

def main():
    """Command line interface"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python apple_music_fetcher.py <apple_music_url> [--detailed]")
        print("  --detailed: Fetch detailed artist/album info for playlist tracks (slower)")
        sys.exit(1)

    url_arg = sys.argv[1] # Renamed 'url' to 'url_arg'
    fetch_details = len(sys.argv) == 3 and sys.argv[2] == '--detailed'

    fetcher = AppleMusicFetcher()

    print("Fetching metadata...")
    if fetch_details:
        print("Note: Detailed mode enabled - this will take longer but provide artist/album info")

    metadata_result = fetcher.fetch_metadata(url_arg, fetch_track_details=fetch_details) # Renamed 'metadata' to 'metadata_result'

    if 'error' in metadata_result:
        print(f"Error: {metadata_result['error']}")
        sys.exit(1)

    # Pretty print the metadata
    try:
        print(json.dumps(metadata_result, indent=2, ensure_ascii=False))
    except UnicodeEncodeError:
        # Fallback for Windows terminals that don't support Unicode
        print(json.dumps(metadata_result, indent=2, ensure_ascii=True))

if __name__ == "__main__":
    main()