#!/usr/bin/env python3
"""
Test script to verify individual track cover art embedding in playlists
"""

import subprocess
import os
from pathlib import Path

def test_playlist_cover_art():
    """Test that playlist tracks get individual cover art, not playlist cover art"""
    
    # Test with a small custom playlist (create a test with just a few tracks)
    test_urls = [
        "https://music.apple.com/us/song/luther/1781270323",  # Single track test
    ]
    
    print("🧪 Testing individual track cover art embedding...")
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n📋 Test {i}: {url}")
        
        try:
            # Run the downloader
            result = subprocess.run([
                "python", "downloader.py", url
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ Download completed successfully")
                print("📋 Output:")
                print(result.stdout)
                
                # Check if individual track cover art was downloaded
                if "📸 Downloading track cover art..." in result.stdout:
                    print("✅ Individual track cover art was downloaded")
                else:
                    print("❌ Individual track cover art was NOT downloaded")
                    
            else:
                print(f"❌ Download failed with return code: {result.returncode}")
                print("Error output:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ Test timed out")
        except Exception as e:
            print(f"❌ Test failed with error: {e}")

if __name__ == "__main__":
    test_playlist_cover_art()
