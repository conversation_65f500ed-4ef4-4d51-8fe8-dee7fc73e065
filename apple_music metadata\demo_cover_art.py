#!/usr/bin/env python3
"""
Demo script showing how to use the cover art URLs
"""

import sys
import json
import requests
from script import AppleMusicFetcher

def download_cover_art(url, filename):
    """Download cover art from URL"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ Downloaded: {filename}")
        return True
    except Exception as e:
        print(f"❌ Failed to download {filename}: {e}")
        return False

def demo_cover_art():
    """Demo cover art extraction and download"""
    url = "https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12"
    
    fetcher = AppleMusicFetcher()
    
    print("Fetching playlist metadata...")
    metadata = fetcher.fetch_metadata(url, fetch_track_details=False)
    
    if 'error' in metadata:
        print(f"Error: {metadata['error']}")
        return
    
    # Get detailed info for first track
    sample_track = metadata['tracks'][0]
    print(f"\nFetching detailed info for: {sample_track['title']}")
    
    detailed_tracks = fetcher._enrich_track_details([sample_track])
    track = detailed_tracks[0]
    
    print(f"\n🎵 Track: {track['title']}")
    print(f"🎤 Artist: {track['artist']}")
    print(f"💿 Album: {track['album']}")
    print(f"⏱️ Duration: {track['duration_formatted']}")
    print(f"🎭 Genre: {track['genre']}")
    print(f"💰 Price: ${track['track_price']} {track['currency']}")
    
    if track.get('cover_art'):
        print(f"\n🖼️ Cover Art URLs:")
        cover_art = track['cover_art']
        
        for size, url in cover_art.items():
            print(f"  {size}: {url}")
        
        # Download a sample cover art
        print(f"\n📥 Downloading cover art...")
        safe_title = "".join(c for c in track['title'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
        
        # Download medium resolution cover art
        if cover_art.get('medium'):
            filename = f"{safe_title}_cover_300x300.jpg"
            download_cover_art(cover_art['medium'], filename)
        
        # Download high resolution cover art
        if cover_art.get('extra_large'):
            filename = f"{safe_title}_cover_1000x1000.jpg"
            download_cover_art(cover_art['extra_large'], filename)
    
    # Show preview URL
    if track.get('preview_url'):
        print(f"\n🎧 Preview URL: {track['preview_url']}")
        print("   (30-second preview - you can play this in a browser or media player)")
    
    # Show Apple Music links
    print(f"\n🔗 Links:")
    print(f"  Track: {track['url']}")
    print(f"  Artist: {track.get('artist_view_url', 'N/A')}")
    print(f"  Album: {track.get('collection_view_url', 'N/A')}")

if __name__ == "__main__":
    demo_cover_art()
