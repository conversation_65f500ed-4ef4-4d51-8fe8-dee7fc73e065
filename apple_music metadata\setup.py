#!/usr/bin/env python3
"""
Setup script for Apple Music to YouTube Music Downloader
"""

import subprocess
import sys
import platform

def install_python_packages():
    """Install required Python packages"""
    print("📦 Installing Python packages...")
    
    packages = [
        'requests>=2.28.0',
        'yt-dlp>=2023.12.30'
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ Installed: {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install: {package}")
            return False
    
    return True

def check_ffmpeg():
    """Check if FFmpeg is installed"""
    print("\n🔧 Checking FFmpeg...")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg is installed")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ FFmpeg is not installed")
    print("\n📋 FFmpeg Installation Instructions:")
    
    system = platform.system().lower()
    
    if system == "windows":
        print("Windows:")
        print("1. Download FFmpeg from: https://ffmpeg.org/download.html#build-windows")
        print("2. Extract the archive")
        print("3. Add the 'bin' folder to your system PATH")
        print("4. Or use chocolatey: choco install ffmpeg")
        print("5. Or use winget: winget install FFmpeg")
    
    elif system == "darwin":  # macOS
        print("macOS:")
        print("1. Install Homebrew: https://brew.sh/")
        print("2. Run: brew install ffmpeg")
    
    elif system == "linux":
        print("Linux:")
        print("Ubuntu/Debian: sudo apt update && sudo apt install ffmpeg")
        print("CentOS/RHEL: sudo yum install ffmpeg")
        print("Fedora: sudo dnf install ffmpeg")
        print("Arch: sudo pacman -S ffmpeg")
    
    return False

def main():
    """Main setup function"""
    print("🎵 Apple Music to YouTube Music Downloader Setup")
    print("=" * 50)
    
    # Install Python packages
    if not install_python_packages():
        print("\n❌ Failed to install Python packages")
        sys.exit(1)
    
    # Check FFmpeg
    ffmpeg_ok = check_ffmpeg()
    
    print("\n" + "=" * 50)
    
    if ffmpeg_ok:
        print("✅ Setup complete! You can now use the downloader.")
        print("\nUsage:")
        print("python downloader.py 'https://music.apple.com/us/playlist/...'")
    else:
        print("⚠️ Setup partially complete. Please install FFmpeg to continue.")
        print("After installing FFmpeg, you can use the downloader.")
    
    print("\n📋 Example usage:")
    print("python downloader.py 'https://music.apple.com/us/playlist/top-100-usa/pl.606afcbb70264d2eb2b51d8dbcfa6a12'")

if __name__ == "__main__":
    main()
